import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertEmployees() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Print current database name for debugging
    const [dbRows] = await connection.query('SELECT DATABASE() as db');
    console.log('Connected to DB:', dbRows[0].db);
    // List all tables in the current database
    const [tables] = await connection.query('SHOW TABLES');
    console.log('Tables in DB:', tables.map(row => Object.values(row)[0]));
    // Delete all records before inserting new ones
    await connection.query('DELETE FROM employees');
    const employees = [];
    const emails = new Set();
    const positions = ['Manager', 'Cashier', 'Vendor', 'Barista'];
    while (employees.length < 50) {
      const firstName = faker.person.firstName();
      const lastName = faker.person.lastName();
      let email = faker.internet.email({ firstName, lastName });
      if (emails.has(email)) continue;
      emails.add(email);
      const position = positions[Math.floor(Math.random() * positions.length)];
      const hireDate = faker.date.past({ years: 10 }).toISOString().split('T')[0];
      const salary = faker.number.float({ min: 200, max: 2000, precision: 0.01 });
      // Generate phone number with max 20 chars, digits and dashes only
      let phone = faker.phone.number('+855-###-###-###');
      phone = phone.replace(/[^0-9\-]/g, '').slice(0, 20);
      const address = faker.location.streetAddress();
      employees.push([
        firstName,
        lastName,
        position,
        hireDate,
        salary,
        phone,
        email,
        address
      ]);
    }
    const sql = `INSERT INTO employees (first_name, last_name, position, hire_date, salary, phone_number, email, address) VALUES ?`;
    await connection.query(sql, [employees]);
    console.log('Inserted 50 employees successfully!');
    // Count records after insert
    const [countRows] = await connection.query('SELECT COUNT(*) as count FROM employees');
    console.log('Total employees in table after insert:', countRows[0].count);

    const [employeeRows] = await connection.query('SELECT * FROM employees');
    console.log('All employees data:', employeeRows);
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertEmployees();
