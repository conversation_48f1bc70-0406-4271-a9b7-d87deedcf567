# ☕ Coffee Management System

A beautiful, full-stack coffee shop management system inspired by Starbucks design, featuring a secure admin portal with comprehensive activity logging and analytics.

## 🌟 Features

### 🎨 Beautiful Design
- **Starbucks-inspired UI**: Modern, green-themed design with smooth gradients
- **Smooth Animations**: Powered by Framer Motion for delightful interactions
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile
- **Dark/Light Theme**: Automatic adaptation based on user preference

### 🔐 Authentication & Security
- **Secure Login System**: Username/password authentication
- **Role-based Access Control**: Admin and user roles with different permissions
- **Session Management**: Automatic token refresh and secure logout
- **Activity Logging**: Track all user actions and API calls

### 📊 Admin Dashboard
- **Real-time Analytics**: Live metrics and performance indicators
- **User Management**: Create, edit, and manage user accounts
- **Employee Management**: Full CRUD operations for staff
- **Order Tracking**: Monitor orders and payment status
- **Security Monitoring**: View security logs and threat detection
- **System Health**: Monitor API performance and system metrics

### 🛡️ Advanced Security
- **Rate Limiting**: Prevent API abuse and DDoS attacks
- **SQL Injection Protection**: Input validation and sanitization
- **XSS Prevention**: Content Security Policy and input filtering
- **Audit Trail**: Complete logging of all system activities
- **Suspicious Activity Detection**: Automatic threat pattern recognition

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- MySQL 8.0+
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ProjectDB-Year2-Term3
   ```

2. **Setup Backend**
   ```bash
   cd Backend
   npm install
   
   # Configure environment variables
   cp .env.example .env
   # Edit .env with your database credentials
   
   # Setup database tables
   node setup-database.js
   
   # Start the server
   npm start
   ```

3. **Setup Frontend**
   ```bash
   cd Frontend
   npm install
   npm install framer-motion
   
   # Start development server
   npm run dev
   ```

4. **Access the Application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000
   - Admin Login: `admin` / `admin123`

## 📁 Project Structure

```
ProjectDB-Year2-Term3/
├── Backend/
│   ├── config/
│   │   ├── database.js          # Database connection
│   │   └── ConnectSQlWithAiven.js
│   ├── controllers/
│   │   └── adminController.js   # Admin API endpoints
│   ├── middleware/
│   │   └── activityLogger.js    # Activity logging middleware
│   ├── routes/
│   │   └── adminRoutes.js       # API routes
│   ├── sql/
│   │   └── create_activity_logging_tables.sql
│   ├── server.js                # Express server
│   └── package.json
├── Frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── auth/
│   │   │   │   └── Login.jsx    # Beautiful login page
│   │   │   ├── pages/
│   │   │   │   └── AdminDashboard.jsx  # Main dashboard
│   │   │   └── services/
│   │   │       └── api.js       # API service layer
│   │   ├── contexts/
│   │   │   └── AuthContext.jsx  # Authentication context
│   │   ├── App.jsx              # Main app component
│   │   └── main.jsx
│   ├── package.json
│   └── .env                     # Frontend environment variables
└── modelDB/
    ├── DDL.sql                  # Database schema
    ├── AccessControl.sql        # Security & audit tables
    └── Query.sql               # Sample queries
```

## 🎯 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/validate` - Validate token

### Admin Dashboard
- `GET /api/admin/dashboard/stats` - Dashboard statistics
- `GET /api/admin/system-metrics` - Real-time system metrics

### User Management
- `GET /api/admin/users` - Get all users
- `POST /api/admin/users` - Create new user
- `PATCH /api/admin/users/:username/deactivate` - Deactivate user
- `PATCH /api/admin/users/:username/unlock` - Unlock user account

### Employee Management
- `GET /api/admin/employees` - Get all employees
- `POST /api/admin/employees` - Create new employee
- `PUT /api/admin/employees/:id` - Update employee
- `DELETE /api/admin/employees/:id` - Delete employee

### Activity & Security Logs
- `GET /api/admin/activity-logs` - Get activity logs
- `GET /api/admin/security-logs` - Get security incident logs
- `GET /api/admin/api-usage-stats` - API usage statistics
- `POST /api/admin/clean-logs` - Clean old log entries

## 🛠️ Configuration

### Backend Environment Variables (.env)
```env
# Environment
NODE_ENV=development

# Local Database
DBP_HOST=localhost
DBP_USER=root
DBP_PASSWORD=12345
DBP_NAME=coffee_management_db
DBP_PORT=3306

# Server
PORT=3000
FRONTEND_URL=http://localhost:5173
```

### Frontend Environment Variables (.env)
```env
VITE_API_URL=http://localhost:3000/api
VITE_APP_NAME=Coffee Manager
VITE_APP_VERSION=1.0.0
```

## 🎨 Design System

### Colors (Starbucks-inspired)
- **Primary Green**: `#059669` (emerald-600)
- **Secondary Green**: `#065f46` (emerald-800)
- **Light Green**: `#ecfdf5` (emerald-50)
- **Accent Colors**: Blue, Purple, Orange for different sections

### Typography
- **Headings**: Inter font family, bold weights
- **Body**: Inter font family, regular weights
- **Code**: Fira Code for monospace elements

### Components
- **Rounded Corners**: Consistent 12px-24px border radius
- **Shadows**: Layered shadow system for depth
- **Animations**: Smooth 300ms transitions with spring physics
- **Glass Effects**: Backdrop blur with semi-transparent backgrounds

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Breakpoints**:
  - Mobile: 0-640px
  - Tablet: 641-1024px
  - Desktop: 1025px+
- **Touch Friendly**: Large buttons and touch targets
- **Keyboard Navigation**: Full keyboard accessibility

## 🔒 Security Features

### Frontend Security
- **Input Validation**: Client-side validation for all forms
- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: Token-based request validation
- **Secure Storage**: Encrypted local storage for sensitive data

### Backend Security
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **SQL Injection Prevention**: Parameterized queries
- **Authentication**: JWT token-based authentication
- **Activity Logging**: Complete audit trail
- **Suspicious Activity Detection**: Pattern-based threat detection

## 📊 Monitoring & Analytics

### Activity Logging
- **API Requests**: All HTTP requests logged with timestamps
- **User Actions**: Login attempts, data modifications
- **Performance Metrics**: Response times, error rates
- **Security Events**: Failed logins, suspicious patterns

### Dashboard Metrics
- **Real-time Stats**: Live user count, active sessions
- **Performance Charts**: Response time trends, error rates
- **Security Overview**: Threat detection, blocked requests
- **Business Metrics**: Revenue, orders, customer growth

## 🚀 Deployment

### Production Checklist
1. Set `NODE_ENV=production`
2. Configure production database
3. Set up SSL certificates
4. Configure reverse proxy (nginx)
5. Set up monitoring and logging
6. Configure backup systems

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👥 Team

- **Backend Development**: Node.js, Express, MySQL
- **Frontend Development**: React, Tailwind CSS, Framer Motion
- **Database Design**: MySQL with advanced security features
- **UI/UX Design**: Starbucks-inspired modern interface

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [FAQ section](#-frequently-asked-questions)
2. Search existing [GitHub Issues](../../issues)
3. Create a new issue with detailed information

## 📈 Roadmap

### Phase 1 (Current)
- ✅ Authentication system
- ✅ Admin dashboard
- ✅ Activity logging
- ✅ Basic CRUD operations

### Phase 2 (Next)
- 🔄 Advanced analytics
- 🔄 Real-time notifications
- 🔄 Mobile app
- 🔄 Advanced reporting

### Phase 3 (Future)
- 📋 Inventory management
- 📋 Point of sale integration
- 📋 Customer loyalty program
- 📋 Multi-location support

---

**Made with ☕ and ❤️ by the Coffee Management Team**
