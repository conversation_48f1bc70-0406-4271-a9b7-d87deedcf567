import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Simple session storage (in production, use Redis or database)
const activeSessions = new Map();

// Debug log
console.log('AuthService initialized');

/**
 * Authentication Service
 * Handles user authentication using .env file for credentials
 */
class AuthService {
    /**
     * Get all users from .env file
     * @returns {Array} Array of user objects
     */
    static getUsers() {
        const users = [];
        
        // Add admin user
        if (process.env.DB_USER && process.env.DB_PASSWORD) {
            users.push({
                username: process.env.DB_USER,
                password: process.env.DB_PASSWORD,
                role: 'admin',
                name: 'Administrator'
            });
        }
        
        // Add other users from .env
        Object.keys(process.env).forEach(key => {
            if (key.startsWith('USER_AND_PASSWORD_AND_HOST_')) {
                const value = process.env[key];
                const [username, password, host, ...roleInfo] = value.split(',');
                
                // Default role is 'user' if not specified
                const role = roleInfo.length > 0 ? roleInfo[0].trim() : 'user';
                
                users.push({
                    username: username.trim(),
                    password: password.trim(),
                    host: host.trim(),
                    role: role,
                    name: username.trim()
                });
            }
        });
        
        return users;
    }
    
    /**
     * Authenticate user
     * @param {string} username - Username
     * @param {string} password - Password
     * @returns {Object|null} User object with session token or null if authentication fails
     */
    static authenticate(username, password) {
        const users = this.getUsers();

        // Find user by username
        const user = users.find(u => u.username === username);

        // Check if user exists and password matches
        if (user && user.password === password) {
            // Generate simple session token
            const sessionToken = this.generateSessionToken();

            // Store session
            activeSessions.set(sessionToken, {
                username: user.username,
                role: user.role,
                name: user.name,
                loginTime: new Date()
            });

            return {
                username: user.username,
                role: user.role,
                name: user.name,
                token: sessionToken
            };
        }

        return null;
    }
    
    /**
     * Generate a simple session token
     * @returns {string} Session token
     */
    static generateSessionToken() {
        return Math.random().toString(36).substring(2, 15) +
               Math.random().toString(36).substring(2, 15);
    }

    /**
     * Verify session token
     * @param {string} token - Session token
     * @returns {Object|null} Session data or null if invalid
     */
    static verifyToken(token) {
        return activeSessions.get(token) || null;
    }

    /**
     * Logout user (invalidate session)
     * @param {string} token - Session token
     * @returns {boolean} Success status
     */
    static logout(token) {
        if (activeSessions.has(token)) {
            activeSessions.delete(token);
            return true;
        }
        return false;
    }
    
    /**
     * Create a new user in .env file
     * @param {Object} userData - User data
     * @returns {boolean} Success status
     */
    static createUser(userData) {
        const { username, password, role = 'user' } = userData;
        
        // Check if username already exists
        const users = this.getUsers();
        if (users.find(u => u.username === username)) {
            return false;
        }
        
        // Get the next available user index
        const userKeys = Object.keys(process.env).filter(key => 
            key.startsWith('USER_AND_PASSWORD_AND_HOST_')
        );
        
        const nextIndex = userKeys.length + 1;
        
        // Add user to .env (this is just a simulation - in a real app, you'd update the .env file)
        process.env[`USER_AND_PASSWORD_AND_HOST_${nextIndex}`] = `${username},${password},localhost,${role}`;
        
        return true;
    }
    
    /**
     * Update user in .env file
     * @param {string} username - Username to update
     * @param {Object} userData - Updated user data
     * @returns {boolean} Success status
     */
    static updateUser(username, userData) {
        const users = this.getUsers();
        const userIndex = users.findIndex(u => u.username === username);
        
        if (userIndex === -1) {
            return false;
        }
        
        // Find the .env key for this user
        const userKeys = Object.keys(process.env).filter(key => 
            key.startsWith('USER_AND_PASSWORD_AND_HOST_')
        );
        
        for (const key of userKeys) {
            const value = process.env[key];
            const [envUsername] = value.split(',');
            
            if (envUsername.trim() === username) {
                // Update user in .env (simulation)
                const newPassword = userData.password || users[userIndex].password;
                const newRole = userData.role || users[userIndex].role;
                
                process.env[key] = `${username},${newPassword},localhost,${newRole}`;
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Delete user from .env file
     * @param {string} username - Username to delete
     * @returns {boolean} Success status
     */
    static deleteUser(username) {
        // Cannot delete admin
        if (username === process.env.DB_USER) {
            return false;
        }
        
        const userKeys = Object.keys(process.env).filter(key => 
            key.startsWith('USER_AND_PASSWORD_AND_HOST_')
        );
        
        for (const key of userKeys) {
            const value = process.env[key];
            const [envUsername] = value.split(',');
            
            if (envUsername.trim() === username) {
                // Delete user from .env (simulation)
                delete process.env[key];
                return true;
            }
        }
        
        return false;
    }
}

export default AuthService;
