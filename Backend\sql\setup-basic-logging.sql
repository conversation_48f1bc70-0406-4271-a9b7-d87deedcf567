-- Simple activity logging tables for the Coffee Management System
-- This creates minimal logging tables that work with the existing database

-- Activity log table for general API activity
CREATE TABLE IF NOT EXISTS activity_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    method VARCHAR(10) NOT NULL,
    url VARCHAR(500) NOT NULL,
    path VARCHAR(500) NOT NULL,
    query_params JSON,
    request_body JSON,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    status_code INT NOT NULL,
    response_time_ms INT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    user_id INT,
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip_address (ip_address),
    INDEX idx_status_code (status_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Security incident log table
CREATE TABLE IF NOT EXISTS security_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    method VARCHAR(10) NOT NULL,
    url VARCHAR(500) NOT NULL,
    user_agent TEXT,
    detected_patterns TEXT,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
    blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert sample data to test the logging system
INSERT INTO activity_log (method, url, path, ip_address, user_agent, status_code, response_time_ms, success) VALUES
('GET', '/api/admin/dashboard/stats', '/api/admin/dashboard/stats', '*************', 'Mozilla/5.0 Test Browser', 200, 150, 1),
('POST', '/api/admin/users', '/api/admin/users', '*************', 'Mozilla/5.0 Test Browser', 201, 300, 1);

SELECT 'Basic activity logging tables created successfully!' as status;
