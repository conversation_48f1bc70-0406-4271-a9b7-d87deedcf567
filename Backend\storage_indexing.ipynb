{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import random"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate and Insert Data into Database - Facebook Users - Generate 6 000 000 records"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import string\n", "import mysql.connector  # Replace with your database connector library (e.g., psycopg2 for PostgreSQL)\n", "\n", "# Database connection details (replace with your credentials)\n", "DB_HOST = \"localhost\"\n", "DB_USER = \"root\"\n", "DB_PASSWORD = \"\"\n", "DB_NAME = \"big_data_db\"\n", "\n", "# Function to generate random data (you can customize this)\n", "def generate_data():\n", "    countries = [\n", "        \"Australia\", \"Brunei\", \n", "        \"Cambodia\", \"Canada\", \n", "        \"Vietnam\", \"France\", \n", "        \"Germany\", \"India\", \n", "        \"Indonesia\", \"Italy\", \n", "        \"Japan\", \"Laos\", \n", "        \"Malaysia\", \"Myanmar\", \n", "        \"Norway\", \"Singapore\",\n", "        \"Spain\", \"Thailand\", \n", "        \"United Kingdom\", \"United States\"\n", "    ]\n", "    name = ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(10))\n", "    email = f\"{name.lower()}@{random.randint(1, 100)}.com\"\n", "    age = random.randint(18, 65)\n", "    country = random.choice(countries)\n", "    return name, email, age, country\n", "\n", "# Connect to the database\n", "try:\n", "    connection = mysql.connector.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)\n", "    cursor = connection.cursor()\n", "except mysql.connector.Error as err:\n", "    print(\"Error connecting to database:\", err)\n", "    exit()\n", "\n", "# Create table if it doesn't exist (replace with your table schema)\n", "create_table_query = \"\"\"\n", "CREATE TABLE IF NOT EXISTS facebook_users (\n", "id INT AUTO_INCREMENT PRIMARY KEY,\n", "name VARCHAR(255) NOT NULL,\n", "email VARCHAR(255) NOT NULL UNIQUE,\n", "age INT NOT NULL,\n", "country VARCHAR(255) NOT NULL\n", ")\n", "\"\"\"\n", "cursor.execute(create_table_query)\n", "connection.commit()\n", "\n", "# Generate and insert data\n", "print('Start inserting ...')\n", "insertion_amount = 100\n", "num_records = 60000\n", "i = 0\n", "for k in range(insertion_amount):\n", "    for _ in range(num_records):\n", "        name, email, age, country = generate_data()\n", "        insert_query = \"\"\"\n", "        INSERT INTO facebook_users (name, email, age, country)\n", "        VALUES (%s, %s, %s, %s)\n", "        \"\"\"\n", "        cursor.execute(insert_query, (name, email, age, country))\n", "    connection.commit()\n", "    i = i + 1\n", "    print('Insertion round', i, num_records, 'records.')\n", "\n", "print(f\"Successfully inserted {num_records*insertion_amount} records into the database.\")\n", "\n", "# Close the connection\n", "cursor.close()\n", "connection.close()\n", "\n", "print('Finished inserting!!!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sample Data Users"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import random\n", "# import string\n", "# import mysql.connector  # Replace with your database connector library (e.g., psycopg2 for PostgreSQL)\n", "\n", "# # Database connection details (replace with your credentials)\n", "# DB_HOST = \"localhost\"\n", "# DB_USER = \"root\"\n", "# DB_PASSWORD = \"\"\n", "# DB_NAME = \"big_data_db\"\n", "\n", "# # Function to generate random data (you can customize this)\n", "# def generate_data():\n", "#     name = ''.join(random.choice(string.ascii_uppercase + string.digits) for _ in range(10))\n", "#     email = f\"{name.lower()}@{random.randint(1, 100)}.com\"\n", "#     age = random.ran<PERSON>t(18, 65)\n", "#     return name, email, age\n", "\n", "# # Connect to the database\n", "# try:\n", "#     connection = mysql.connector.connect(host=DB_HOST, user=DB_USER, password=DB_PASSWORD, database=DB_NAME)\n", "#     cursor = connection.cursor()\n", "# except mysql.connector.Error as err:\n", "#     print(\"Error connecting to database:\", err)\n", "#     exit()\n", "\n", "# # Create table if it doesn't exist (replace with your table schema)\n", "# create_table_query = \"\"\"\n", "# CREATE TABLE IF NOT EXISTS users (\n", "#   id INT AUTO_INCREMENT PRIMARY KEY,\n", "#   name VARCHAR(255) NOT NULL,\n", "#   email VARCHAR(255) NOT NULL UNIQUE,\n", "#   age INT NOT NULL\n", "# )\n", "# \"\"\"\n", "# cursor.execute(create_table_query)\n", "# connection.commit()\n", "\n", "# # Generate and insert data\n", "# print('Start inserting ...')\n", "# insertion_amount = 100\n", "# num_records = 100000\n", "# i = 0\n", "# for k in range(insertion_amount):\n", "#     for _ in range(num_records):\n", "#         name, email, age = generate_data()\n", "#         insert_query = \"\"\"\n", "#         INSERT INTO users (name, email, age)\n", "#         VALUES (%s, %s, %s)\n", "#         \"\"\"\n", "#         cursor.execute(insert_query, (name, email, age))\n", "#     connection.commit()\n", "#     i = i + 1\n", "#     print('Insertion round', i, num_records, 'records.')\n", "\n", "# print(f\"Successfully inserted {num_records*insertion_amount} records into the database.\")\n", "\n", "# # Close the connection\n", "# cursor.close()\n", "# connection.close()\n", "\n", "# print('Finished inserting!!!')\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}