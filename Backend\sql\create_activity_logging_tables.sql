-- Activity logging tables for the Coffee Management System
-- Run this script to create tables for comprehensive activity logging

-- Activity log table for general API activity
CREATE TABLE IF NOT EXISTS activity_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    method VARCHAR(10) NOT NULL,
    url VARCHAR(500) NOT NULL,
    path VARCHAR(500) NOT NULL,
    query_params JSON,
    request_body JSON,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    status_code INT NOT NULL,
    response_time_ms INT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    user_id INT,
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip_address (ip_address),
    INDEX idx_status_code (status_code),
    INDEX idx_user_id (user_id),
    INDEX idx_method_url (method, url(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Security incident log table
CREATE TABLE IF NOT EXISTS security_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    method VARCHAR(10) NOT NULL,
    url VARCHAR(500) NOT NULL,
    user_agent TEXT,
    detected_patterns TEXT,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
    blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip_address (ip_address),
    INDEX idx_severity (severity),
    INDEX idx_blocked (blocked)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Rate limiting log table
CREATE TABLE IF NOT EXISTS rate_limit_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) NOT NULL,
    request_count INT NOT NULL,
    window_start DATETIME NOT NULL,
    blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Error log table for detailed error tracking
CREATE TABLE IF NOT EXISTS error_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    error_type VARCHAR(100) NOT NULL,
    error_message TEXT NOT NULL,
    stack_trace TEXT,
    request_method VARCHAR(10),
    request_url VARCHAR(500),
    ip_address VARCHAR(45),
    user_id INT,
    severity ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_error_type (error_type),
    INDEX idx_severity (severity),
    INDEX idx_resolved (resolved)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Performance metrics table
CREATE TABLE IF NOT EXISTS performance_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    endpoint VARCHAR(500) NOT NULL,
    method VARCHAR(10) NOT NULL,
    response_time_ms INT NOT NULL,
    memory_usage_mb DECIMAL(10,2),
    cpu_usage_percent DECIMAL(5,2),
    db_query_count INT DEFAULT 0,
    db_query_time_ms INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_endpoint (endpoint(100)),
    INDEX idx_response_time (response_time_ms)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Login attempt log table for authentication tracking
CREATE TABLE IF NOT EXISTS login_log (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    username VARCHAR(100),
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(255),
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address),
    INDEX idx_success (success)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API endpoint usage statistics view
CREATE OR REPLACE VIEW api_usage_stats AS
SELECT 
    path,
    method,
    COUNT(*) as total_requests,
    AVG(response_time_ms) as avg_response_time,
    MIN(response_time_ms) as min_response_time,
    MAX(response_time_ms) as max_response_time,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed_requests,
    ROUND((SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate
FROM activity_log 
GROUP BY path, method
ORDER BY total_requests DESC;

-- Security incidents by IP view
CREATE OR REPLACE VIEW security_incidents_by_ip AS
SELECT 
    ip_address,
    COUNT(*) as incident_count,
    MAX(timestamp) as last_incident,
    GROUP_CONCAT(DISTINCT severity ORDER BY severity) as severity_levels,
    SUM(CASE WHEN blocked = 1 THEN 1 ELSE 0 END) as blocked_count
FROM security_log 
GROUP BY ip_address
ORDER BY incident_count DESC;

-- Daily activity summary view
CREATE OR REPLACE VIEW daily_activity_summary AS
SELECT 
    DATE(timestamp) as activity_date,
    COUNT(*) as total_requests,
    COUNT(DISTINCT ip_address) as unique_ips,
    AVG(response_time_ms) as avg_response_time,
    SUM(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status_code >= 400 THEN 1 ELSE 0 END) as error_count
FROM activity_log 
GROUP BY DATE(timestamp)
ORDER BY activity_date DESC;

-- Create stored procedures for common log queries

DELIMITER //

-- Procedure to get activity logs with pagination
CREATE PROCEDURE GetActivityLogs(
    IN p_limit INT DEFAULT 50,
    IN p_offset INT DEFAULT 0,
    IN p_start_date DATETIME DEFAULT NULL,
    IN p_end_date DATETIME DEFAULT NULL,
    IN p_ip_address VARCHAR(45) DEFAULT NULL
)
BEGIN
    SELECT 
        log_id,
        timestamp,
        method,
        url,
        ip_address,
        status_code,
        response_time_ms,
        success,
        error_message,
        user_id
    FROM activity_log 
    WHERE 
        (p_start_date IS NULL OR timestamp >= p_start_date) AND
        (p_end_date IS NULL OR timestamp <= p_end_date) AND
        (p_ip_address IS NULL OR ip_address = p_ip_address)
    ORDER BY timestamp DESC
    LIMIT p_limit OFFSET p_offset;
END //

-- Procedure to get security incidents
CREATE PROCEDURE GetSecurityIncidents(
    IN p_limit INT DEFAULT 50,
    IN p_severity VARCHAR(20) DEFAULT NULL
)
BEGIN
    SELECT 
        log_id,
        timestamp,
        ip_address,
        method,
        url,
        detected_patterns,
        severity,
        blocked
    FROM security_log 
    WHERE 
        (p_severity IS NULL OR severity = p_severity)
    ORDER BY timestamp DESC
    LIMIT p_limit;
END //

-- Procedure to clean old logs
CREATE PROCEDURE CleanOldLogs(
    IN p_days_to_keep INT DEFAULT 90
)
BEGIN
    DECLARE cutoff_date DATETIME;
    SET cutoff_date = DATE_SUB(NOW(), INTERVAL p_days_to_keep DAY);
    
    DELETE FROM activity_log WHERE timestamp < cutoff_date;
    DELETE FROM security_log WHERE timestamp < cutoff_date;
    DELETE FROM rate_limit_log WHERE timestamp < cutoff_date;
    DELETE FROM error_log WHERE timestamp < cutoff_date AND resolved = 1;
    DELETE FROM performance_log WHERE timestamp < cutoff_date;
    DELETE FROM login_log WHERE timestamp < cutoff_date;
END //

DELIMITER ;

-- Insert sample data to test the logging system
INSERT INTO activity_log (method, url, path, ip_address, user_agent, status_code, response_time_ms, success) VALUES
('GET', '/api/admin/dashboard/stats', '/api/admin/dashboard/stats', '*************', 'Mozilla/5.0 Test Browser', 200, 150, 1),
('POST', '/api/admin/users', '/api/admin/users', '*************', 'Mozilla/5.0 Test Browser', 201, 300, 1),
('GET', '/api/admin/employees', '/api/admin/employees', '*************', 'Mozilla/5.0 Test Browser', 200, 120, 1);

INSERT INTO security_log (ip_address, method, url, detected_patterns, severity, blocked) VALUES
('********', 'GET', '/api/admin/users?id=1 UNION SELECT * FROM users', 'SQL injection pattern detected', 'HIGH', 0),
('********', 'POST', '/api/admin/users', 'XSS pattern detected in request body', 'MEDIUM', 0);

-- Show table creation confirmation
SELECT 'Activity logging tables created successfully!' as status;
