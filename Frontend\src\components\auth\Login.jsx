import React, { useState, useEffect } from 'react';
import { Coffee, Eye, EyeOff, User, Lock, Sparkles, Star } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const Login = ({ onLogin }) => {
    const [formData, setFormData] = useState({
        username: '',
        password: ''
    });
    const [showPassword, setShowPassword] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const [particles, setParticles] = useState([]);

    // Generate floating particles for animation
    useEffect(() => {
        const newParticles = Array.from({ length: 20 }, (_, i) => ({
            id: i,
            x: Math.random() * 100,
            y: Math.random() * 100,
            size: Math.random() * 4 + 2,
            duration: Math.random() * 20 + 10,
            delay: Math.random() * 5
        }));
        setParticles(newParticles);
    }, []);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
        if (error) setError('');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            // Mock authentication - replace with actual API call
            if (formData.username === 'admin' && formData.password === 'admin123') {
                onLogin({
                    username: formData.username,
                    role: 'admin',
                    token: 'mock-jwt-token'
                });
            } else {
                throw new Error('Invalid credentials');
            }
        } catch (err) {
            setError('Invalid username or password. Try admin/admin123');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="h-screen w-full flex flex-col justify-center items-center bg-gradient-to-br from-emerald-50 via-green-50 to-emerald-100 overflow-hidden">
    {/* Responsive meta tag for mobile */}
    {/* If using CRA or Vite, ensure this is in your index.html: <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\"> */}
            {/* Animated Background Particles */}
            <div className="absolute inset-0 overflow-hidden">
                {particles.map((particle) => (
                    <motion.div
                        key={particle.id}
                        className="absolute rounded-full bg-green-200/30"
                        style={{
                            width: particle.size,
                            height: particle.size,
                            left: `${particle.x}%`,
                            top: `${particle.y}%`,
                        }}
                        animate={{
                            y: [0, -100, 0],
                            opacity: [0, 1, 0],
                            scale: [0.5, 1, 0.5],
                        }}
                        transition={{
                            duration: particle.duration,
                            repeat: Infinity,
                            delay: particle.delay,
                            ease: "easeInOut"
                        }}
                    />
                ))}
            </div>

            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                
                }} />
            </div>

            <div className="relative z-10 min-h-screen w-full flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-10"
            style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23059669' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                padding: '20px'
               
            }}  >
                <motion.div
                    initial={{ opacity: 0, scale: 0.9, y: 20 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    transition={{ duration: 0.6, ease: "easeOut" }}
                    className="w-full max-w-xs md:max-w-sm lg:max-w-md flex flex-col flex-1 justify-center items-center"  
                >
                    {/* Logo and Title */}
                    <div className="w-full flex flex-col items-center mb-2">
                        <motion.div
                            className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-emerald-600 to-green-700 rounded-full mb-3 shadow-lg"
                            whileHover={{ scale: 1.05, rotate: 5 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <Coffee className="w-10 h-10 text-white" />
                        </motion.div>
                        <motion.h1
                            className="text-3xl font-bold bg-gradient-to-r from-emerald-700 to-green-800 bg-clip-text text-transparent mb-1"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2, duration: 0.6 }}
                        >
                            Coffee Manager
                        </motion.h1>
                        <motion.p
                            className="text-emerald-600 font-medium"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.3, duration: 0.6 }}
                        >
                            Welcome to your coffee universe
                        </motion.p>
                    </div>

                    {/* Login Form */}
                    <motion.div
                        className="bg-white/80 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/50 w-full flex-1 flex flex-col justify-center p-3 sm:p-4 md:p-6"
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4, duration: 0.6 }}
                        style={{
                            
                        }}
                    >
                        <form onSubmit={handleSubmit} className="space-y-6">
                            {/* Username Field */}
                            <div className="space-y-2">
                                <label className="text-sm font-semibold text-gray-700 flex items-center">
                                    <User className="w-4 h-4 mr-2 text-emerald-600" />
                                    Username
                                </label>
                                <motion.div
                                    className="relative"
                                    whileFocus={{ scale: 1.02 }}
                                >
                                    <input
                                        type="text"
                                        name="username"
                                        value={formData.username}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-emerald-500 focus:outline-none transition-all duration-300 bg-white/70 placeholder-gray-400 text-black"
                                        placeholder="Enter your username"
                                        required
                                    />
                                    <div className="absolute inset-y-0 right-4 flex items-center">
                                        <Sparkles className="w-5 h-5 text-emerald-400" />
                                    </div>
                                </motion.div>
                            </div>

                            {/* Password Field */}
                            <div className="space-y-2">
                                <label className="text-sm font-semibold text-gray-700 flex items-center">
                                    <Lock className="w-4 h-4 mr-2 text-emerald-600" />
                                    Password
                                </label>
                                <motion.div
                                    className="relative"
                                    whileFocus={{ scale: 1.02 }}
                                >
                                    <input
                                        type={showPassword ? "text" : "password"}
                                        name="password"
                                        value={formData.password}
                                        onChange={handleInputChange}
                                        className="w-full px-4 py-4 rounded-2xl border-2 border-gray-200 focus:border-emerald-500 focus:outline-none transition-all duration-300 bg-white/70 placeholder-gray-400 pr-12 text-black"
                                        placeholder="Enter your password"
                                        required
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute inset-y-0 right-4 flex items-center text-gray-400 hover:text-emerald-600 transition-colors"
                                    >
                                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                                    </button>
                                </motion.div>
                            </div>

                            {/* Error Message */}
                            <AnimatePresence>
                                {error && (
                                    <motion.div
                                        initial={{ opacity: 0, height: 0 }}
                                        animate={{ opacity: 1, height: 'auto' }}
                                        exit={{ opacity: 0, height: 0 }}
                                        className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl text-sm"
                                    >
                                        {error}
                                    </motion.div>
                                )}
                            </AnimatePresence>

                            {/* Login Button */}
                            <motion.button
                                type="submit"
                                disabled={isLoading}
                                className="w-full bg-gradient-to-r from-emerald-600 to-green-700 text-white font-semibold py-4 rounded-2xl shadow-lg hover:shadow-xl transform transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                            >
                                <AnimatePresence mode="wait">
                                    {isLoading ? (
                                        <motion.div
                                            key="loading"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            exit={{ opacity: 0 }}
                                            className="flex items-center justify-center"
                                        >
                                            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                                            Signing in...
                                        </motion.div>
                                    ) : (
                                        <motion.span
                                            key="text"
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            exit={{ opacity: 0 }}
                                            className="flex items-center justify-center"
                                        >
                                            <Star className="w-5 h-5 mr-2" />
                                            Sign In
                                        </motion.span>
                                    )}
                                </AnimatePresence>
                            </motion.button>
                        </form>

                        {/* Demo Credentials */}
                        <motion.div
                            className="mt-4 p-4 bg-emerald-50 rounded-xl border border-emerald-200 w-full text-center"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.6 }}
                        >
                            <p className="text-sm text-emerald-700 font-medium mb-2">Demo Credentials:</p>
                            <p className="text-xs text-emerald-600">Username: admin | Password: admin123</p>
                        </motion.div>
                    </motion.div>
                </motion.div>
            </div>

            {/* Floating Coffee Beans */}
            <div className="absolute top-10 left-10">
                <motion.div
                    animate={{ rotate: 360, y: [0, -20, 0] }}
                    transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
                    className="w-8 h-8 bg-amber-600 rounded-full opacity-20"
                />
            </div>
            <div className="absolute bottom-20 right-20">
                <motion.div
                    animate={{ rotate: -360, y: [0, -30, 0] }}
                    transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 2 }}
                    className="w-6 h-6 bg-emerald-600 rounded-full opacity-20"
                />
            </div>
        </div>
    );
};

export default Login;
