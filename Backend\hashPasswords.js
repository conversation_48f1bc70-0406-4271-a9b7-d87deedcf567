import bcrypt from 'bcryptjs';
import { connectToAivenDB } from './src/config/ConnectSQlWithAiven.js';

const defaultUsers = [
    { username: 'db_admin', password: 'DbAdmin123!', role: 'admin' },
    { username: 'manager1', password: 'Manager123!', role: 'manager' },
    { username: 'manager2', password: 'Manager456!', role: 'manager' },
    { username: 'cashier1', password: 'Cashier123!', role: 'cashier' },
    { username: 'cashier2', password: 'Cashier456!', role: 'cashier' },
    { username: 'cashier3', password: 'Cashier789!', role: 'cashier' },
    { username: 'barista1', password: 'Barista123!', role: 'barista' },
    { username: 'barista2', password: 'Barista456!', role: 'barista' },
    { username: 'vendor1', password: 'Vendor123!', role: 'vendor' },
    { username: 'vendor2', password: 'Vendor456!', role: 'vendor' }
];

async function hashPasswords() {
    try {
        const connection = await connectToAivenDB();
        
        console.log('🔐 Hashing passwords for default users...');
        
        for (const user of defaultUsers) {
            // Check if user exists
            const [existingUsers] = await connection.execute(
                'SELECT user_name FROM user_security_context WHERE user_name = ?',
                [user.username]
            );
            
            if (existingUsers.length > 0) {
                // Hash the password
                const saltRounds = 12;
                const hashedPassword = await bcrypt.hash(user.password, saltRounds);
                
                // Update the password hash
                await connection.execute(
                    'UPDATE user_security_context SET password_hash = ? WHERE user_name = ?',
                    [hashedPassword, user.username]
                );
                
                console.log(`✅ Updated password hash for user: ${user.username}`);
            } else {
                console.log(`⚠️  User not found: ${user.username}`);
            }
        }
        
        connection.release();
        console.log('🎉 Password hashing completed!');
        
        // Test login for one user
        console.log('\n🧪 Testing login...');
        const testPassword = 'DbAdmin123!';
        const [testUser] = await (await connectToAivenDB()).execute(
            'SELECT password_hash FROM user_security_context WHERE user_name = ?',
            ['db_admin']
        );
        
        if (testUser.length > 0) {
            const isValid = await bcrypt.compare(testPassword, testUser[0].password_hash);
            console.log(`Test login for db_admin: ${isValid ? '✅ SUCCESS' : '❌ FAILED'}`);
        }
        
        console.log('\n📝 You can now use these credentials to login:');
        defaultUsers.forEach(user => {
            console.log(`   Username: ${user.username} | Password: ${user.password} | Role: ${user.role}`);
        });
        
    } catch (error) {
        console.error('❌ Error hashing passwords:', error);
        process.exit(1);
    }
}

hashPasswords();
