import fs from 'fs/promises';
import path from 'path';

const envFilePath = path.join(process.cwd(), '.env');

/**
 * Reads and logs the entire content of the .env file.
 * @returns {Promise<string>} The content of the file.
 */
async function readEnvFile() {
  try {
    const data = await fs.readFile(envFilePath, 'utf8');
    console.log('--- Reading .env file ---');
    console.log(data);
    return data;
  } catch (err) {
    console.error(`Error reading file: ${err.message}`);
    throw err;
  }
}

/**
 * Appends a new key-value pair to the .env file (Create).
 * @param {string} key The environment variable key.
 * @param {string} value The environment variable value.
 */
async function appendToEnvFile(key, value) {
  const line = `\n${key}=${value}`;
  try {
    await fs.appendFile(envFilePath, line, 'utf8');
    console.log(`--- Appended to .env file ---\nAdded: ${key}=${value}`);
  } catch (err) {
    console.error(`Error appending to file: ${err.message}`);
    throw err;
  }
}

/**
 * Updates an existing key in the .env file. If the key doesn't exist, it adds it.
 * @param {string} keyToUpdate The key of the variable to update.
 * @param {string} newValue The new value for the variable.
 */
async function updateEnvVariable(keyToUpdate, newValue) {
  try {
    const data = await fs.readFile(envFilePath, 'utf8');
    const lines = data.split('\n');
    let keyFound = false;

    const newLines = lines.map(line => {
      if (line.startsWith(`${keyToUpdate}=`)) {
        keyFound = true;
        return `${keyToUpdate}=${newValue}`;
      }
      return line;
    });

    if (!keyFound) {
      newLines.push(`${keyToUpdate}=${newValue}`);
    }

    const updatedData = newLines.join('\n');
    await fs.writeFile(envFilePath, updatedData, 'utf8');
    console.log(`--- Updated .env file ---\nSet ${keyToUpdate} to ${newValue}`);
  } catch (err) {
    console.error(`Error updating file: ${err.message}`);
    throw err;
  }
}

/**
 * Deletes a key-value pair from the .env file.
 * @param {string} keyToDelete The key of the variable to delete.
 */
async function deleteEnvVariable(keyToDelete) {
  try {
    const data = await fs.readFile(envFilePath, 'utf8');
    const lines = data.split('\n');

    const newLines = lines.filter(line => !line.startsWith(`${keyToDelete}=`));

    const updatedData = newLines.join('\n');
    await fs.writeFile(envFilePath, updatedData, 'utf8');
    console.log(`--- Updated .env file ---\nDeleted key: ${keyToDelete}`);
  } catch (err) {
    console.error(`Error updating file: ${err.message}`);
    throw err;
  }
}

/**
 * Overwrites the .env file with a new set of variables.
 * This is a destructive write operation.
 * @param {Object} envVariables An object like { KEY: 'value', ... }.
 */
async function writeEnvFile(envVariables) {
    const content = Object.entries(envVariables)
        .map(([key, value]) => `${key}=${value}`)
        .join('\n');
    
    try {
        await fs.writeFile(envFilePath, content, 'utf8');
        console.log('--- Wrote to .env file ---\nFile content has been overwritten.');
    } catch (err) {
        console.error(`Error writing to file: ${err.message}`);
        throw err;
    }
}


// --- Example Usage ---
// You can uncomment and run these functions to test them.
// To run: node manageEnv.js

/*
async function main() {
  // 1. READ the file
  await readEnvFile();

  // 2. APPEND (Create) a new variable
  await appendToEnvFile('API_KEY', 'xyz123abc');
  
  // 3. UPDATE a variable
  await updateEnvVariable('PORT', '8080');

  // 4. DELETE a variable
  await deleteEnvVariable('API_KEY');

  // 5. WRITE (overwrite) the entire file
  const newConfig = {
    DB_HOST: 'localhost',
    DB_USER: 'admin',
    DB_PASSWORD: 'password'
  };
  // await writeEnvFile(newConfig); // Be careful with this one!
  
  console.log('\n--- Done ---');
}

main();
*/