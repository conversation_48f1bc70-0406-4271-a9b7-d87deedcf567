import AuthService from '../services/authService.js';

/**
 * Authentication middleware
 * Verifies JW<PERSON> token and adds user info to request
 */
export const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({
            success: false,
            message: 'Access token required'
        });
    }

    const session = AuthService.verifyToken(token);
    if (!session) {
        return res.status(403).json({
            success: false,
            message: 'Invalid or expired session'
        });
    }

    req.user = session;
    next();
};

/**
 * Admin role middleware
 * Ensures user has admin role
 */
export const requireAdmin = (req, res, next) => {
    if (!req.user || req.user.role !== 'admin') {
        return res.status(403).json({
            success: false,
            message: 'Admin access required'
        });
    }
    next();
};

/**
 * Role-based access control middleware
 * @param {Array} allowedRoles - Array of allowed roles
 */
export const requireRole = (allowedRoles) => {
    return (req, res, next) => {
        if (!req.user || !allowedRoles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
            });
        }
        next();
    };
};

/**
 * Optional authentication middleware
 * Adds user info if token is present but doesn't require it
 */
export const optionalAuth = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
        const session = AuthService.verifyToken(token);
        if (session) {
            req.user = session;
        }
    }

    next();
};
