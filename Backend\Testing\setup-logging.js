import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
    host: process.env.DBP_HOST || 'localhost',
    user: process.env.DBP_USER || 'root',
    password: process.env.DBP_PASSWORD || '12345',
    database: process.env.DBP_NAME || 'coffee_management_db',
    port: parseInt(process.env.DBP_PORT) || 3306,
    multipleStatements: true
};

async function setupActivityLogging() {
    let connection;
    
    try {
        console.log('🔗 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Connected to database successfully!');
        
        // Read the SQL file
        const sqlFilePath = path.join(__dirname, 'sql', 'create_activity_logging_tables.sql');
        const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
        
        console.log('📊 Creating activity logging tables...');
        
        // Split SQL content by statements and execute one by one
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);
        
        for (const statement of statements) {
            if (statement.toLowerCase().includes('create') || 
                statement.toLowerCase().includes('delimiter') ||
                statement.toLowerCase().includes('drop')) {
                try {
                    await connection.execute(statement);
                } catch (error) {
                    // Skip delimiter statements and other non-critical errors
                    if (!statement.toLowerCase().includes('delimiter')) {
                        console.log(`⚠️ Warning: ${error.message}`);
                    }
                }
            }
        }
        
        console.log('✅ Activity logging tables created successfully!');
        console.log('\n📋 The following tables were created:');
        console.log('   - activity_log (general API activity)');
        console.log('   - security_log (security incidents)');
        console.log('   - rate_limit_log (rate limiting events)');
        console.log('   - error_log (detailed error tracking)');
        console.log('   - performance_log (performance metrics)');
        console.log('   - login_log (authentication attempts)');
        console.log('\n📈 Views created:');
        console.log('   - api_usage_stats');
        console.log('   - security_incidents_by_ip');
        console.log('   - daily_activity_summary');
        console.log('\n⚙️ Stored procedures created:');
        console.log('   - GetActivityLogs');
        console.log('   - GetSecurityIncidents');
        console.log('   - CleanOldLogs');
        
        console.log('\n🎉 Activity logging setup completed successfully!');
        console.log('You can now start the server to begin logging activities.');
        
    } catch (error) {
        console.error('❌ Error setting up activity logging:', error.message);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.error('💡 Check your database credentials in the .env file');
        } else if (error.code === 'ECONNREFUSED') {
            console.error('💡 Make sure your MySQL server is running');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.error('💡 The database does not exist. Create it first or check the database name');
        }
        
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔒 Database connection closed.');
        }
    }
}

// Run the setup
setupActivityLogging();
