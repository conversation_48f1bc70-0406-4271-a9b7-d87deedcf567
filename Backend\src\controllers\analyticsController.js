import DatabaseService from '../services/databaseService.js';

/**
 * Analytics Controller
 * Implements all complex queries from Query.sql for reporting and analytics
 */

class AnalyticsController {
    /**
     * Get loyalty points per customer
     */
    static async getLoyaltyPointsPerCustomer(req, res) {
        try {
            const loyaltyPoints = await DatabaseService.executeQuery(
                `SELECT 
                    c.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
                    SUM(lp.points_earned) AS total_points
                FROM loyalty_points lp
                JOIN orders o ON lp.order_id = o.order_id
                JOIN customers c ON o.customer_id = c.customer_id
                GROUP BY c.customer_id
                ORDER BY total_points DESC`
            );

            res.json({
                success: true,
                message: 'Loyalty points per customer retrieved successfully',
                data: loyaltyPoints
            });
        } catch (error) {
            console.error('Error getting loyalty points per customer:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve loyalty points per customer',
                error: error.message
            });
        }
    }

    /**
     * Get material costs per month (eating and object materials)
     */
    static async getMaterialCostsPerMonth(req, res) {
        try {
            const materialCosts = await DatabaseService.executeQuery(
                `SELECT 
                    'eating' AS material_type,
                    DATE_FORMAT(import_date, '%Y-%m') AS month,
                    SUM(unit_price * quantity) AS total_cost
                FROM material_eating
                GROUP BY month
                
                UNION
                
                SELECT 
                    'object' AS material_type,
                    DATE_FORMAT(import_date, '%Y-%m') AS month,
                    SUM(unit_price * quantity) AS total_cost
                FROM material_object
                GROUP BY month
                ORDER BY month DESC, material_type`
            );

            res.json({
                success: true,
                message: 'Material costs per month retrieved successfully',
                data: materialCosts
            });
        } catch (error) {
            console.error('Error getting material costs per month:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve material costs per month',
                error: error.message
            });
        }
    }

    /**
     * Get products with zero quantity materials
     */
    static async getProductsWithZeroQuantityMaterials(req, res) {
        try {
            const products = await DatabaseService.executeQuery(
                `SELECT 
                    pr.product_name,
                    me.name_material AS material_eating,
                    mo.name_material AS material_object,
                    COALESCE(me.quantity, 0) as eating_quantity,
                    COALESCE(mo.quantity, 0) as object_quantity
                FROM products pr
                LEFT JOIN material_eating me ON pr.material_eating_id = me.material_eating_id
                LEFT JOIN material_object mo ON pr.material_object_id = mo.material_object_id
                WHERE (me.quantity = 0 OR mo.quantity = 0)`
            );

            res.json({
                success: true,
                message: 'Products with zero quantity materials retrieved successfully',
                data: products
            });
        } catch (error) {
            console.error('Error getting products with zero quantity materials:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve products with zero quantity materials',
                error: error.message
            });
        }
    }

    /**
     * Get sales in last N days (default 7)
     */
    static async getSalesLastDays(req, res) {
        try {
            const { days = 7 } = req.query;
            
            const sales = await DatabaseService.executeQuery(
                `SELECT 
                    DATE(order_date) AS date,
                    SUM(quantity * unit_price) AS total_sales,
                    COUNT(*) as order_count
                FROM orders
                WHERE order_date >= NOW() - INTERVAL ? DAY
                GROUP BY DATE(order_date)
                ORDER BY date DESC`,
                [days]
            );

            res.json({
                success: true,
                message: `Sales for last ${days} days retrieved successfully`,
                data: sales
            });
        } catch (error) {
            console.error('Error getting sales last days:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve sales for last days',
                error: error.message
            });
        }
    }

    /**
     * Get product sold the most (by quantity)
     */
    static async getMostSoldProduct(req, res) {
        try {
            const product = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.product_name,
                    SUM(o.quantity) AS total_quantity_sold
                FROM orders o
                JOIN products p ON o.product_id = p.product_id
                GROUP BY o.product_id
                ORDER BY total_quantity_sold DESC
                LIMIT 1`
            );

            res.json({
                success: true,
                message: 'Most sold product retrieved successfully',
                data: product
            });
        } catch (error) {
            console.error('Error getting most sold product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve most sold product',
                error: error.message
            });
        }
    }

    /**
     * Get spending on supplies (grouped by supplier)
     */
    static async getSupplierSpending(req, res) {
        try {
            const spending = await DatabaseService.executeQuery(
                `SELECT 
                    s.supplies_name,
                    s.category,
                    SUM(COALESCE(me.unit_price * me.quantity, 0) + COALESCE(mo.unit_price * mo.quantity, 0)) AS total_spent
                FROM suppliers s
                LEFT JOIN material_eating me ON s.supplier_id = me.supplier_id
                LEFT JOIN material_object mo ON s.supplier_id = mo.supplier_id
                GROUP BY s.supplier_id
                ORDER BY total_spent DESC`
            );

            res.json({
                success: true,
                message: 'Supplier spending retrieved successfully',
                data: spending
            });
        } catch (error) {
            console.error('Error getting supplier spending:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve supplier spending',
                error: error.message
            });
        }
    }

    /**
     * Get customer order history
     */
    static async getCustomerOrderHistory(req, res) {
        try {
            const { customer_id } = req.query;
            
            let query = `
                SELECT 
                    c.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
                    o.order_id,
                    p.product_name,
                    o.quantity,
                    o.unit_price,
                    o.order_date,
                    (o.quantity * o.unit_price) as total_amount
                FROM orders o
                JOIN customers c ON o.customer_id = c.customer_id
                JOIN products p ON o.product_id = p.product_id
            `;
            
            let params = [];
            
            if (customer_id) {
                query += ' WHERE c.customer_id = ?';
                params.push(customer_id);
            }
            
            query += ' ORDER BY c.customer_id, o.order_date DESC';

            const orderHistory = await DatabaseService.executeQuery(query, params);

            res.json({
                success: true,
                message: 'Customer order history retrieved successfully',
                data: orderHistory
            });
        } catch (error) {
            console.error('Error getting customer order history:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve customer order history',
                error: error.message
            });
        }
    }

    /**
     * Get sales by month
     */
    static async getSalesByMonth(req, res) {
        try {
            const sales = await DatabaseService.executeQuery(
                `SELECT 
                    DATE_FORMAT(order_date, '%Y-%m') AS month,
                    SUM(quantity * unit_price) AS total_sales,
                    COUNT(*) as order_count
                FROM orders
                GROUP BY month
                ORDER BY month DESC`
            );

            res.json({
                success: true,
                message: 'Sales by month retrieved successfully',
                data: sales
            });
        } catch (error) {
            console.error('Error getting sales by month:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve sales by month',
                error: error.message
            });
        }
    }

    /**
     * Get total revenue by product
     */
    static async getRevenueByProduct(req, res) {
        try {
            const revenue = await DatabaseService.executeQuery(
                `SELECT 
                    p.product_name,
                    p.category_name,
                    SUM(o.quantity * o.unit_price) AS total_revenue,
                    SUM(o.quantity) as total_quantity_sold
                FROM orders o
                JOIN products p ON o.product_id = p.product_id
                GROUP BY p.product_id
                ORDER BY total_revenue DESC`
            );

            res.json({
                success: true,
                message: 'Revenue by product retrieved successfully',
                data: revenue
            });
        } catch (error) {
            console.error('Error getting revenue by product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve revenue by product',
                error: error.message
            });
        }
    }

    /**
     * Get top customers by spending
     */
    static async getTopCustomersBySpending(req, res) {
        try {
            const { limit = 5 } = req.query;
            
            const customers = await DatabaseService.executeQuery(
                `SELECT 
                    c.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
                    c.email,
                    SUM(o.quantity * o.unit_price) AS total_spent,
                    COUNT(o.order_id) as total_orders
                FROM orders o
                JOIN customers c ON o.customer_id = c.customer_id
                GROUP BY c.customer_id
                ORDER BY total_spent DESC
                LIMIT ?`,
                [parseInt(limit)]
            );

            res.json({
                success: true,
                message: `Top ${limit} customers by spending retrieved successfully`,
                data: customers
            });
        } catch (error) {
            console.error('Error getting top customers by spending:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve top customers by spending',
                error: error.message
            });
        }
    }

    /**
     * Get unused products (never ordered)
     */
    static async getUnusedProducts(req, res) {
        try {
            const products = await DatabaseService.executeQuery(
                `SELECT 
                    p.product_id,
                    p.product_name,
                    p.category_name,
                    p.unit_price
                FROM products p
                LEFT JOIN orders o ON p.product_id = o.product_id
                WHERE o.order_id IS NULL`
            );

            res.json({
                success: true,
                message: 'Unused products retrieved successfully',
                data: products
            });
        } catch (error) {
            console.error('Error getting unused products:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve unused products',
                error: error.message
            });
        }
    }

    /**
     * Get most frequently ordered product (by order count)
     */
    static async getMostFrequentlyOrderedProduct(req, res) {
        try {
            const product = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.product_name,
                    p.category_name,
                    COUNT(o.order_id) AS times_ordered,
                    SUM(o.quantity) as total_quantity
                FROM orders o
                JOIN products p ON o.product_id = p.product_id
                GROUP BY o.product_id
                ORDER BY times_ordered DESC
                LIMIT 1`
            );

            res.json({
                success: true,
                message: 'Most frequently ordered product retrieved successfully',
                data: product
            });
        } catch (error) {
            console.error('Error getting most frequently ordered product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve most frequently ordered product',
                error: error.message
            });
        }
    }

    /**
     * Get monthly order count and revenue
     */
    static async getMonthlyOrderStats(req, res) {
        try {
            const stats = await DatabaseService.executeQuery(
                `SELECT 
                    DATE_FORMAT(order_date, '%Y-%m') AS month,
                    COUNT(order_id) AS total_orders,
                    SUM(quantity * unit_price) AS total_revenue,
                    AVG(quantity * unit_price) AS average_order_value
                FROM orders
                GROUP BY month
                ORDER BY month DESC`
            );

            res.json({
                success: true,
                message: 'Monthly order statistics retrieved successfully',
                data: stats
            });
        } catch (error) {
            console.error('Error getting monthly order stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve monthly order statistics',
                error: error.message
            });
        }
    }
}

export default AnalyticsController;
