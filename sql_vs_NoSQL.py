"""
Create a .env file in the same directory as this script with the following content:

# MySQL configuration
DBP_HOST=localhost
DBP_USER=root
DBP_PASSWORD=12345
DBP_NAME=coffee_management_db
DBP_PORT=3306

# MongoDB configuration
MONGO_URI=mongodb://localhost:27017
MONGO_DB=coffee_management_db
MONGO_COLLECTION=coffee_records
"""

import time
import mysql.connector
from pymongo import MongoClient
import matplotlib.pyplot as plt
import random
import string

# MySQL configuration (hardcoded values)
MYSQL_HOST = "localhost"
MYSQL_USER = "root"
MYSQL_PASSWORD = "12345"
MYSQL_DB = "coffee_management_db"
MYSQL_PORT = 3306

# MongoDB configuration (hardcoded values)
MONGO_URI = "mongodb://localhost:27017"
MONGO_DB = "coffee_management_db"
MONGO_COLLECTION = "coffee_records"

# Dataset sizes
SMALL_SIZE = 1000
LARGE_SIZE = 100000

# Number of iterations for averaging
ITERATIONS = 3

# Function to generate random string
def random_string(length=10):
    return ''.join(random.choices(string.ascii_letters, k=length))

# Sample data generation
def generate_sample_data(size):
    return [
        {
            "coffee_type": random_string(),
            "price": round(random.uniform(2.5, 10.0), 2),
            "origin": random_string(15)
        }
        for _ in range(size)
    ]

# MySQL CRUD operations
def mysql_crud(size):
    conn = mysql.connector.connect(
        host=MYSQL_HOST,
        user=MYSQL_USER,
        password=MYSQL_PASSWORD,
        database=MYSQL_DB,
        port=MYSQL_PORT
    )
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS coffee_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            coffee_type VARCHAR(255),
            price FLOAT,
            origin VARCHAR(255)
        )
    """)
    
    data = generate_sample_data(size)
    
    # Create
    insert_query = "INSERT INTO coffee_records (coffee_type, price, origin) VALUES (%s, %s, %s)"
    start = time.time()
    for record in data:
        cursor.execute(insert_query, (record["coffee_type"], record["price"], record["origin"]))
    conn.commit()
    create_time = time.time() - start
    
    # Read
    start = time.time()
    cursor.execute("SELECT * FROM coffee_records WHERE price > 5.0")
    _ = cursor.fetchall()
    read_time = time.time() - start
    
    # Update
    update_query = "UPDATE coffee_records SET price = price * 1.1 WHERE price < 5.0"
    start = time.time()
    cursor.execute(update_query)
    conn.commit()
    update_time = time.time() - start
    
    # Delete
    delete_query = "DELETE FROM coffee_records WHERE price > 8.0"
    start = time.time()
    cursor.execute(delete_query)
    conn.commit()
    delete_time = time.time() - start
    
    # Clean up
    cursor.execute("DROP TABLE coffee_records")
    conn.commit()
    cursor.close()
    conn.close()
    
    return create_time, read_time, update_time, delete_time

# MongoDB CRUD operations
def mongodb_crud(size):
    client = MongoClient(MONGO_URI)
    db = client[MONGO_DB]
    collection = db[MONGO_COLLECTION]
    
    data = generate_sample_data(size)
    
    # Create
    start = time.time()
    collection.insert_many(data)
    create_time = time.time() - start
    
    # Read
    start = time.time()
    _ = list(collection.find({"price": {"$gt": 5.0}}))
    read_time = time.time() - start
    
    # Update
    start = time.time()
    collection.update_many({"price": {"$lt": 5.0}}, {"$mul": {"price": 1.1}})
    update_time = time.time() - start
    
    # Delete
    start = time.time()
    collection.delete_many({"price": {"$gt": 8.0}})
    delete_time = time.time() - start
    
    # Clean up
    collection.drop()
    client.close()
    
    return create_time, read_time, update_time, delete_time

# Function to run experiments and collect averages
def run_experiments():
    results = {
        "mysql_small": {"create": [], "read": [], "update": [], "delete": []},
        "mysql_large": {"create": [], "read": [], "update": [], "delete": []},
        "mongodb_small": {"create": [], "read": [], "update": [], "delete": []},
        "mongodb_large": {"create": [], "read": [], "update": [], "delete": []}
    }
    
    for _ in range(ITERATIONS):
        # Small dataset
        mc, mr, mu, md = mysql_crud(SMALL_SIZE)
        results["mysql_small"]["create"].append(mc)
        results["mysql_small"]["read"].append(mr)
        results["mysql_small"]["update"].append(mu)
        results["mysql_small"]["delete"].append(md)
        
        moc, mor, mou, mod = mongodb_crud(SMALL_SIZE)
        results["mongodb_small"]["create"].append(moc)
        results["mongodb_small"]["read"].append(mor)
        results["mongodb_small"]["update"].append(mou)
        results["mongodb_small"]["delete"].append(mod)
        
        # Large dataset
        mc, mr, mu, md = mysql_crud(LARGE_SIZE)
        results["mysql_large"]["create"].append(mc)
        results["mysql_large"]["read"].append(mr)
        results["mysql_large"]["update"].append(mu)
        results["mysql_large"]["delete"].append(md)
        
        moc, mor, mou, mod = mongodb_crud(LARGE_SIZE)
        results["mongodb_large"]["create"].append(moc)
        results["mongodb_large"]["read"].append(mor)
        results["mongodb_large"]["update"].append(mou)
        results["mongodb_large"]["delete"].append(mod)
    
    # Calculate averages
    averages = {}
    for key in results:
        averages[key] = {
            op: sum(times) / len(times) for op, times in results[key].items()
        }
    
    return averages

# Function to plot results
def plot_results(averages):
    operations = ["create", "read", "update", "delete"]
    datasets = ["small", "large"]
    databases = ["mysql", "mongodb"]
    
    for dataset in datasets:
        plt.figure(figsize=(12, 6))
        for db in databases:
            times = [averages[f"{db}_{dataset}"][op] for op in operations]
            plt.bar([f"{db}_{op}" for op in operations], times, label=db)
        
        plt.ylabel('Time (seconds)')
        plt.title(f'CRUD Operation Times for {dataset.capitalize()} Dataset')
        plt.legend()
        plt.grid(True, axis='y')
        plt.savefig(f'crud_comparison_{dataset}.png')
        plt.show()

# Main execution
if __name__ == "__main__":
    averages = run_experiments()
    plot_results(averages)
    
    # Print averages
    for key, ops in averages.items():
        print(f"{key.capitalize()}:")
        for op, time in ops.items():
            print(f"  {op.capitalize()}: {time:.4f} seconds")
