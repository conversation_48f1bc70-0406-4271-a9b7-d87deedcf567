-- -- =====================================================
-- -- COFFEE MANAGEMENT DATABASE - ACCESS CONTROL SCRIPT
-- -- =====================================================
-- -- This script creates users and roles with specific permissions
-- -- for different types of database users in the coffee shop system

-- USE coffee_management_db;

-- -- =====================================================
-- -- 1. CREATE ROLES FOR DIFFERENT USER TYPES
-- -- =====================================================

-- -- Create role for Manager (full access)
-- CREATE ROLE 'manager_role';

-- -- Create role for Cashier (order processing, payments)
-- CREATE ROLE 'cashier_role';

-- -- Create role for Barista (view orders, update order status)
-- CREATE ROLE 'barista_role';

-- -- Create role for Vendor (inventory management)
-- CREATE ROLE 'vendor_role';

-- -- Create role for Customer (limited access for self-service)
-- CREATE ROLE 'customer_role';

-- -- Create role for Admin (database administration)
-- CREATE ROLE 'admin_role';

-- -- =====================================================
-- -- 2. GRANT PERMISSIONS TO ROLES
-- -- =====================================================

-- -- MANAGER ROLE - Full business operations access
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.customers TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.employees TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.suppliers TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.material_eating TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.material_object TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.products TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.orders TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.payments TO 'manager_role';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON coffee_management_db.loyalty_points TO 'manager_role';

-- -- CASHIER ROLE - Order processing and payments
-- GRANT SELECT ON coffee_management_db.customers TO 'cashier_role';
-- GRANT INSERT, UPDATE ON coffee_management_db.customers TO 'cashier_role';
-- GRANT SELECT ON coffee_management_db.products TO 'cashier_role';
-- GRANT SELECT, INSERT, UPDATE ON coffee_management_db.orders TO 'cashier_role';
-- GRANT SELECT, INSERT ON coffee_management_db.payments TO 'cashier_role';
-- GRANT SELECT ON coffee_management_db.loyalty_points TO 'cashier_role';
-- GRANT SELECT ON coffee_management_db.material_eating TO 'cashier_role';
-- GRANT SELECT ON coffee_management_db.material_object TO 'cashier_role';

-- -- BARISTA ROLE - View orders and update order status
-- GRANT SELECT ON coffee_management_db.orders TO 'barista_role';
-- GRANT UPDATE(status_order) ON coffee_management_db.orders TO 'barista_role';
-- GRANT SELECT ON coffee_management_db.products TO 'barista_role';
-- GRANT SELECT ON coffee_management_db.customers TO 'barista_role';
-- GRANT SELECT ON coffee_management_db.material_eating TO 'barista_role';
-- GRANT SELECT ON coffee_management_db.material_object TO 'barista_role';

-- -- VENDOR ROLE - Inventory and supplier management
-- GRANT SELECT, INSERT, UPDATE ON coffee_management_db.suppliers TO 'vendor_role';
-- GRANT SELECT, INSERT, UPDATE ON coffee_management_db.material_eating TO 'vendor_role';
-- GRANT SELECT, INSERT, UPDATE ON coffee_management_db.material_object TO 'vendor_role';
-- GRANT SELECT ON coffee_management_db.products TO 'vendor_role';

-- -- CUSTOMER ROLE - Limited self-service access
-- GRANT SELECT ON coffee_management_db.products TO 'customer_role';
-- GRANT SELECT(customer_id, first_name, last_name, email, phone_number, registration_date) ON coffee_management_db.customers TO 'customer_role';
-- GRANT SELECT ON coffee_management_db.orders TO 'customer_role';
-- GRANT SELECT ON coffee_management_db.loyalty_points TO 'customer_role';

-- -- ADMIN ROLE - Database administration
-- GRANT ALL PRIVILEGES ON coffee_management_db.* TO 'admin_role';
-- GRANT CREATE USER, DROP USER, RELOAD, PROCESS, FILE, REFERENCES, INDEX, ALTER, SHOW DATABASES, SUPER, CREATE TEMPORARY TABLES, LOCK TABLES, EXECUTE, REPLICATION SLAVE, REPLICATION CLIENT, CREATE VIEW, SHOW VIEW, CREATE ROUTINE, ALTER ROUTINE, CREATE USER, EVENT, TRIGGER ON *.* TO 'admin_role';

-- -- =====================================================
-- -- 3. CREATE USERS AND ASSIGN ROLES
-- -- =====================================================

-- -- Create Manager users
-- CREATE USER 'manager1'@'localhost' IDENTIFIED BY 'Manager123!';
-- CREATE USER 'manager2'@'localhost' IDENTIFIED BY 'Manager456!';
-- GRANT 'manager_role' TO 'manager1'@'localhost';
-- GRANT 'manager_role' TO 'manager2'@'localhost';

-- -- Create Cashier users
-- CREATE USER 'cashier1'@'localhost' IDENTIFIED BY 'Cashier123!';
-- CREATE USER 'cashier2'@'localhost' IDENTIFIED BY 'Cashier456!';
-- CREATE USER 'cashier3'@'localhost' IDENTIFIED BY 'Cashier789!';
-- GRANT 'cashier_role' TO 'cashier1'@'localhost';
-- GRANT 'cashier_role' TO 'cashier2'@'localhost';
-- GRANT 'cashier_role' TO 'cashier3'@'localhost';

-- -- Create Barista users
-- CREATE USER 'barista1'@'localhost' IDENTIFIED BY 'Barista123!';
-- CREATE USER 'barista2'@'localhost' IDENTIFIED BY 'Barista456!';
-- GRANT 'barista_role' TO 'barista1'@'localhost';
-- GRANT 'barista_role' TO 'barista2'@'localhost';

-- -- Create Vendor users
-- CREATE USER 'vendor1'@'localhost' IDENTIFIED BY 'Vendor123!';
-- CREATE USER 'vendor2'@'localhost' IDENTIFIED BY 'Vendor456!';
-- GRANT 'vendor_role' TO 'vendor1'@'localhost';
-- GRANT 'vendor_role' TO 'vendor2'@'localhost';

-- -- Create Customer users (for self-service kiosks or mobile app)
-- CREATE USER 'customer_app'@'localhost' IDENTIFIED BY 'CustomerApp123!';
-- GRANT 'customer_role' TO 'customer_app'@'localhost';

-- -- Create Admin user
-- CREATE USER 'db_admin'@'localhost' IDENTIFIED BY 'DbAdmin123!';
-- GRANT 'admin_role' TO 'db_admin'@'localhost';

-- -- Create Application user (for backend services)
-- CREATE USER 'app_backend'@'localhost' IDENTIFIED BY 'AppBackend123!';
-- GRANT 'manager_role' TO 'app_backend'@'localhost'; -- Give full access to backend

-- -- =====================================================
-- -- 4. SET DEFAULT ROLES FOR USERS
-- -- =====================================================

-- SET DEFAULT ROLE 'manager_role' TO 'manager1'@'localhost';
-- SET DEFAULT ROLE 'manager_role' TO 'manager2'@'localhost';
-- SET DEFAULT ROLE 'cashier_role' TO 'cashier1'@'localhost';
-- SET DEFAULT ROLE 'cashier_role' TO 'cashier2'@'localhost';
-- SET DEFAULT ROLE 'cashier_role' TO 'cashier3'@'localhost';
-- SET DEFAULT ROLE 'barista_role' TO 'barista1'@'localhost';
-- SET DEFAULT ROLE 'barista_role' TO 'barista2'@'localhost';
-- SET DEFAULT ROLE 'vendor_role' TO 'vendor1'@'localhost';
-- SET DEFAULT ROLE 'vendor_role' TO 'vendor2'@'localhost';
-- SET DEFAULT ROLE 'customer_role' TO 'customer_app'@'localhost';
-- SET DEFAULT ROLE 'admin_role' TO 'db_admin'@'localhost';
-- SET DEFAULT ROLE 'manager_role' TO 'app_backend'@'localhost';

-- -- =====================================================
-- -- 5. CREATE VIEWS FOR ROLE-BASED DATA ACCESS
-- -- =====================================================

-- -- View for Cashiers - Order summary with customer info
-- CREATE VIEW cashier_order_view AS
-- SELECT 
--     o.order_id,
--     o.order_date,
--     CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
--     c.phone_number,
--     p.product_name,
--     o.quantity,
--     o.unit_price,
--     (o.quantity * o.unit_price) AS total_amount,
--     o.status_order
-- FROM orders o
-- JOIN customers c ON o.customer_id = c.customer_id
-- JOIN products p ON o.product_id = p.product_id;

-- -- View for Baristas - Pending orders only
-- CREATE VIEW barista_pending_orders AS
-- SELECT 
--     o.order_id,
--     o.order_date,
--     CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
--     p.product_name,
--     o.quantity,
--     o.status_order
-- FROM orders o
-- JOIN customers c ON o.customer_id = c.customer_id
-- JOIN products p ON o.product_id = p.product_id
-- WHERE o.status_order = 'Pending'
-- ORDER BY o.order_date ASC;

-- -- View for Vendors - Inventory status
-- CREATE VIEW vendor_inventory_view AS
-- SELECT 
--     'eating' AS material_type,
--     me.name_material,
--     me.quantity,
--     me.unit_price,
--     me.import_date,
--     s.supplies_name AS supplier_name
-- FROM material_eating me
-- JOIN suppliers s ON me.supplier_id = s.supplier_id

-- UNION ALL

-- SELECT 
--     'object' AS material_type,
--     mo.name_material,
--     mo.quantity,
--     mo.unit_price,
--     mo.import_date,
--     s.supplies_name AS supplier_name
-- FROM material_object mo
-- JOIN suppliers s ON mo.supplier_id = s.supplier_id;

-- -- View for Customers - Their orders and loyalty points
-- CREATE VIEW customer_dashboard AS
-- SELECT 
--     c.customer_id,
--     CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
--     o.order_id,
--     o.order_date,
--     p.product_name,
--     o.quantity,
--     o.unit_price,
--     (o.quantity * o.unit_price) AS total_amount,
--     o.status_order,
--     IFNULL(lp.points_earned, 0) AS points_earned
-- FROM customers c
-- LEFT JOIN orders o ON c.customer_id = o.customer_id
-- LEFT JOIN products p ON o.product_id = p.product_id
-- LEFT JOIN loyalty_points lp ON o.order_id = lp.order_id;

-- -- =====================================================
-- -- 6. GRANT VIEW PERMISSIONS TO ROLES
-- -- =====================================================

-- GRANT SELECT ON coffee_management_db.cashier_order_view TO 'cashier_role';
-- GRANT SELECT ON coffee_management_db.barista_pending_orders TO 'barista_role';
-- GRANT SELECT ON coffee_management_db.vendor_inventory_view TO 'vendor_role';
-- GRANT SELECT ON coffee_management_db.customer_dashboard TO 'customer_role';
-- GRANT SELECT ON coffee_management_db.cashier_order_view TO 'manager_role';
-- GRANT SELECT ON coffee_management_db.barista_pending_orders TO 'manager_role';
-- GRANT SELECT ON coffee_management_db.vendor_inventory_view TO 'manager_role';
-- GRANT SELECT ON coffee_management_db.customer_dashboard TO 'manager_role';

-- -- =====================================================
-- -- 7. SECURITY POLICIES AND CONSTRAINTS
-- -- =====================================================

-- -- Create audit table for tracking sensitive operations
-- CREATE TABLE audit_log (
--     log_id INT AUTO_INCREMENT PRIMARY KEY,
--     table_name VARCHAR(50) NOT NULL,
--     operation_type ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
--     user_name VARCHAR(100) NOT NULL,
--     operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
--     old_values JSON,
--     new_values JSON
-- );

-- -- Grant audit log access to admin and manager
-- GRANT SELECT, INSERT ON coffee_management_db.audit_log TO 'admin_role';
-- GRANT SELECT ON coffee_management_db.audit_log TO 'manager_role';

-- -- =====================================================
-- -- 8. FLUSH PRIVILEGES
-- -- =====================================================

-- FLUSH PRIVILEGES;

-- -- =====================================================
-- -- 9. DISPLAY CREATED USERS AND THEIR ROLES
-- -- =====================================================

-- SELECT 
--     'USERS AND ROLES CREATED SUCCESSFULLY' AS status,
--     '' AS message
-- UNION ALL
-- SELECT 
--     'Manager Users:' AS status,
--     'manager1, manager2' AS message
-- UNION ALL
-- SELECT 
--     'Cashier Users:' AS status,
--     'cashier1, cashier2, cashier3' AS message
-- UNION ALL
-- SELECT 
--     'Barista Users:' AS status,
--     'barista1, barista2' AS message
-- UNION ALL
-- SELECT 
--     'Vendor Users:' AS status,
--     'vendor1, vendor2' AS message
-- UNION ALL
-- SELECT 
--     'Customer App User:' AS status,
--     'customer_app' AS message
-- UNION ALL
-- SELECT 
--     'Admin User:' AS status,
--     'db_admin' AS message
-- UNION ALL
-- SELECT 
--     'Backend App User:' AS status,
--     'app_backend' AS message;
