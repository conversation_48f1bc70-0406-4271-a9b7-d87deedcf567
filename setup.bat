@echo off
echo ===================================
echo Coffee Management System Setup
echo ===================================
echo.

echo [1/3] Setting up Backend...
cd Backend
echo Installing backend dependencies...
call npm install
echo.

echo [2/3] Setting up Frontend...
cd ..\Frontend
echo Installing frontend dependencies...
call npm install
echo.

echo [3/3] Setup Complete!
echo.
echo ===================================
echo NEXT STEPS:
echo ===================================
echo 1. Set up your MySQL database:
echo    - Run: mysql -u root -p coffee_management_db ^< modelDB/DDL.sql
echo    - Run: mysql -u root -p coffee_management_db ^< modelDB/AccessControl.sql
echo    - Run: mysql -u root -p coffee_management_db ^< modelDB/AdvancedSecurity.sql
echo    - Run: mysql -u root -p coffee_management_db ^< modelDB/ManageAccessControl.sql
echo    - Run: mysql -u root -p coffee_management_db ^< modelDB/SampleData.sql
echo.
echo 2. Start the backend server:
echo    - cd Backend
echo    - npm run dev
echo.
echo 3. Start the frontend (in a new terminal):
echo    - cd Frontend
echo    - npm run dev
echo.
echo 4. Open your browser and go to:
echo    - http://localhost:5173
echo.
echo ===================================
echo Admin Interface Ready!
echo ===================================
pause
