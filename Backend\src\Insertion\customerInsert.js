

import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertCustomers() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Truncate all related tables with foreign key checks disabled for a clean reset
    await connection.query('SET FOREIGN_KEY_CHECKS = 0');
    await connection.query('TRUNCATE TABLE payments');
    await connection.query('TRUNCATE TABLE loyalty_points');
    await connection.query('TRUNCATE TABLE orders');
    await connection.query('TRUNCATE TABLE customers');
    await connection.query('SET FOREIGN_KEY_CHECKS = 1');
    await connection.query('ALTER TABLE customers AUTO_INCREMENT = 1');
    const customers = [];
    const emails = new Set();
    while (customers.length < 1000) {
      const firstName = faker.person.firstName();
      const lastName = faker.person.lastName();
      let email = faker.internet.email({ firstName, lastName });
      if (emails.has(email)) continue;
      emails.add(email);
      // Generate phone number with max 20 chars, digits and dashes only
      let phone = faker.phone.number('+855-###-###-###');
      phone = phone.replace(/[^0-9\-]/g, '').slice(0, 20);
      customers.push([firstName, lastName, email, phone]);
    }
    const sql = `INSERT INTO customers (first_name, last_name, email, phone_number) VALUES ?`;
    await connection.query(sql, [customers]);
    console.log('Inserted 1000 customers successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

async function insertEmployees() {
  let connection;
  try {
    connection = await connectToAivenDB();
    const employees = [];
    const emails = new Set();
    const positions = ['Manager', 'Cashier', 'Vendor', 'Barista'];
    while (employees.length < 50) {
      const firstName = faker.person.firstName();
      const lastName = faker.person.lastName();
      let email = faker.internet.email({ firstName, lastName });
      if (emails.has(email)) continue;
      emails.add(email);
      const position = positions[Math.floor(Math.random() * positions.length)];
      const hireDate = faker.date.past({ years: 10 }).toISOString().split('T')[0];
      const salary = faker.number.float({ min: 200, max: 2000, precision: 0.01 });
      const phone = faker.phone.number('+855########');
      const address = faker.location.streetAddress();
      employees.push([
        firstName,
        lastName,
        position,
        hireDate,
        salary,
        phone,
        email,
        address
      ]);
    }
    const sql = `INSERT INTO employees (first_name, last_name, position, hire_date, salary, phone_number, email, address) VALUES ?`;
    await connection.query(sql, [employees]);
    console.log('Inserted 50 employees successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}
insertCustomers();

// Uncomment to insert customers
// insertCustomers();

// Insert employees
// insertEmployees();