import DatabaseService from '../services/databaseService.js';

/**
 * Order Controller
 * Handles all order-related operations
 */

class OrderController {
    /**
     * Get all orders with pagination and filtering
     */
    static async getAllOrders(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const customer_id = req.query.customer_id || '';
            const status = req.query.status || '';
            const date_from = req.query.date_from || '';
            const date_to = req.query.date_to || '';

            let baseQuery = `
                SELECT 
                    o.order_id,
                    o.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    o.product_id,
                    p.product_name,
                    o.order_date,
                    o.quantity,
                    o.unit_price,
                    o.status_order,
                    (o.quantity * o.unit_price) as total_amount
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products p ON o.product_id = p.product_id
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (customer_id) {
                whereConditions.push('o.customer_id = ?');
                params.push(customer_id);
            }
            
            if (status) {
                whereConditions.push('o.status_order = ?');
                params.push(status);
            }
            
            if (date_from) {
                whereConditions.push('DATE(o.order_date) >= ?');
                params.push(date_from);
            }
            
            if (date_to) {
                whereConditions.push('DATE(o.order_date) <= ?');
                params.push(date_to);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY o.order_date DESC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Orders retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting orders:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve orders',
                error: error.message
            });
        }
    }

    /**
     * Get order by ID
     */
    static async getOrderById(req, res) {
        try {
            const { id } = req.params;
            
            const order = await DatabaseService.executeQuerySingle(
                `SELECT 
                    o.order_id,
                    o.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    c.email as customer_email,
                    o.product_id,
                    p.product_name,
                    p.category_name,
                    o.order_date,
                    o.quantity,
                    o.unit_price,
                    o.status_order,
                    (o.quantity * o.unit_price) as total_amount
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products p ON o.product_id = p.product_id
                WHERE o.order_id = ?`,
                [id]
            );

            if (!order) {
                return res.status(404).json({
                    success: false,
                    message: 'Order not found'
                });
            }

            res.json({
                success: true,
                message: 'Order retrieved successfully',
                data: order
            });
        } catch (error) {
            console.error('Error getting order:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve order',
                error: error.message
            });
        }
    }

    /**
     * Create new order
     */
    static async createOrder(req, res) {
        try {
            const { 
                customer_id, 
                product_id, 
                quantity, 
                unit_price 
            } = req.body;

            // Validation
            if (!product_id || !quantity || !unit_price) {
                return res.status(400).json({
                    success: false,
                    message: 'Required fields: product_id, quantity, unit_price'
                });
            }

            // Validate quantity and unit_price are positive
            if (quantity <= 0 || unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Quantity and unit_price must be greater than 0'
                });
            }

            // Check if customer exists (if provided)
            if (customer_id) {
                const customerExists = await DatabaseService.recordExists('customers', 'customer_id', customer_id);
                if (!customerExists) {
                    return res.status(404).json({
                        success: false,
                        message: 'Customer not found'
                    });
                }
            }

            // Check if product exists
            const productExists = await DatabaseService.recordExists('products', 'product_id', product_id);
            if (!productExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
            }

            const orderId = await DatabaseService.executeInsert(
                `INSERT INTO orders (customer_id, product_id, quantity, unit_price) 
                 VALUES (?, ?, ?, ?)`,
                [customer_id || null, product_id, quantity, unit_price]
            );

            const newOrder = await DatabaseService.executeQuerySingle(
                `SELECT 
                    o.order_id,
                    o.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    o.product_id,
                    p.product_name,
                    o.order_date,
                    o.quantity,
                    o.unit_price,
                    o.status_order,
                    (o.quantity * o.unit_price) as total_amount
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products p ON o.product_id = p.product_id
                WHERE o.order_id = ?`,
                [orderId]
            );

            res.status(201).json({
                success: true,
                message: 'Order created successfully',
                data: newOrder
            });
        } catch (error) {
            console.error('Error creating order:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create order',
                error: error.message
            });
        }
    }

    /**
     * Update order status
     */
    static async updateOrderStatus(req, res) {
        try {
            const { id } = req.params;
            const { status_order } = req.body;

            // Validate status enum
            const validStatuses = ['Pending', 'Completed'];
            if (!validStatuses.includes(status_order)) {
                return res.status(400).json({
                    success: false,
                    message: `Status must be one of: ${validStatuses.join(', ')}`
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                'UPDATE orders SET status_order = ? WHERE order_id = ?',
                [status_order, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Order not found'
                });
            }

            const updatedOrder = await DatabaseService.executeQuerySingle(
                `SELECT 
                    o.order_id,
                    o.customer_id,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    o.product_id,
                    p.product_name,
                    o.order_date,
                    o.quantity,
                    o.unit_price,
                    o.status_order,
                    (o.quantity * o.unit_price) as total_amount
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products p ON o.product_id = p.product_id
                WHERE o.order_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Order status updated successfully',
                data: updatedOrder
            });
        } catch (error) {
            console.error('Error updating order status:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update order status',
                error: error.message
            });
        }
    }

    /**
     * Delete order (only if no payment exists)
     */
    static async deleteOrder(req, res) {
        try {
            const { id } = req.params;

            // Check if order has payment
            const hasPayment = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM payments WHERE order_id = ? LIMIT 1',
                [id]
            );

            if (hasPayment) {
                return res.status(409).json({
                    success: false,
                    message: 'Cannot delete order with existing payment'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                'DELETE FROM orders WHERE order_id = ?',
                [id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Order not found'
                });
            }

            res.json({
                success: true,
                message: 'Order deleted successfully'
            });
        } catch (error) {
            console.error('Error deleting order:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete order',
                error: error.message
            });
        }
    }

    /**
     * Get orders without payment
     */
    static async getOrdersWithoutPayment(req, res) {
        try {
            const orders = await DatabaseService.executeQuery(
                `SELECT 
                    o.order_id,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    p.product_name,
                    o.quantity,
                    o.unit_price,
                    (o.quantity * o.unit_price) as total_amount,
                    o.order_date
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products p ON o.product_id = p.product_id
                LEFT JOIN payments pay ON o.order_id = pay.order_id
                WHERE pay.order_id IS NULL
                ORDER BY o.order_date DESC`
            );

            res.json({
                success: true,
                message: 'Orders without payment retrieved successfully',
                data: orders
            });
        } catch (error) {
            console.error('Error getting orders without payment:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve orders without payment',
                error: error.message
            });
        }
    }
}

export default OrderController;
