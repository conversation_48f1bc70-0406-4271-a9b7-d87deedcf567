import express from 'express';
import CustomerController from '../controllers/customerController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       required:
 *         - first_name
 *         - last_name
 *         - email
 *         - phone_number
 *       properties:
 *         customer_id:
 *           type: integer
 *           description: Auto-generated customer ID
 *         first_name:
 *           type: string
 *           maxLength: 50
 *           description: Customer's first name
 *         last_name:
 *           type: string
 *           maxLength: 50
 *           description: Customer's last name
 *         email:
 *           type: string
 *           maxLength: 100
 *           format: email
 *           description: Customer's email address (unique)
 *         phone_number:
 *           type: string
 *           maxLength: 20
 *           description: Customer's phone number
 *         registration_date:
 *           type: string
 *           format: date-time
 *           description: Customer registration timestamp
 *       example:
 *         customer_id: 1
 *         first_name: "<PERSON>"
 *         last_name: "Doe"
 *         email: "<EMAIL>"
 *         phone_number: "+1234567890"
 *         registration_date: "2024-01-15T10:30:00Z"
 *
 *     CustomerInput:
 *       type: object
 *       required:
 *         - first_name
 *         - last_name
 *         - email
 *         - phone_number
 *       properties:
 *         first_name:
 *           type: string
 *           maxLength: 50
 *         last_name:
 *           type: string
 *           maxLength: 50
 *         email:
 *           type: string
 *           maxLength: 100
 *           format: email
 *         phone_number:
 *           type: string
 *           maxLength: 20
 *       example:
 *         first_name: "John"
 *         last_name: "Doe"
 *         email: "<EMAIL>"
 *         phone_number: "+1234567890"
 */

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: Get all customers with pagination
 *     tags: [Customers]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of customers per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or email
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Customer'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     hasNext:
 *                       type: boolean
 *                     hasPrev:
 *                       type: boolean
 */
router.get('/', CustomerController.getAllCustomers);

/**
 * @swagger
 * /api/customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *       404:
 *         description: Customer not found
 */
router.get('/:id', CustomerController.getCustomerById);

/**
 * @swagger
 * /api/customers:
 *   post:
 *     summary: Create a new customer
 *     tags: [Customers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomerInput'
 *     responses:
 *       201:
 *         description: Customer created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.post('/', CustomerController.createCustomer);

/**
 * @swagger
 * /api/customers/{id}:
 *   put:
 *     summary: Update customer
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomerInput'
 *     responses:
 *       200:
 *         description: Customer updated successfully
 *       404:
 *         description: Customer not found
 *       409:
 *         description: Email already exists
 */
router.put('/:id', CustomerController.updateCustomer);

/**
 * @swagger
 * /api/customers/{id}:
 *   delete:
 *     summary: Delete customer
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 *       404:
 *         description: Customer not found
 *       409:
 *         description: Cannot delete customer with existing orders
 */
router.delete('/:id', CustomerController.deleteCustomer);

/**
 * @swagger
 * /api/customers/{id}/orders:
 *   get:
 *     summary: Get customer order history
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Customer ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of orders per page
 *     responses:
 *       200:
 *         description: Customer orders retrieved successfully
 *       404:
 *         description: Customer not found
 */
router.get('/:id/orders', CustomerController.getCustomerOrders);

export default router;
