import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertMaterialObject() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Get supplier and employee IDs for foreign keys
    const [suppliers] = await connection.query('SELECT supplier_id FROM suppliers');
    const [employees] = await connection.query('SELECT employee_id FROM employees');
    console.log('Suppliers found:', suppliers.length, 'Employees found:', employees.length);
    if (suppliers.length === 0 || employees.length === 0) {
      throw new Error('Suppliers or Employees table is empty. Please insert data first.');
    }
    const categories = [
      'tissue', 'plastic cup', 'paper cup', 'cup lid', 'straw', 'plastic bag',
      'napkin', 'glove', 'mask', 'tray', 'cleaning spray', 'dishwashing liquid',
      'cloth towel', 'toilet paper', 'garbage bag', 'ice bucket', 'measuring spoon',
      'milk frother', 'filter paper', 'receipt roll'
    ];
    const materials = [];
    for (let i = 0; i < 1500; i++) {
      const supplier = suppliers[Math.floor(Math.random() * suppliers.length)].supplier_id;
      const employee = employees[Math.floor(Math.random() * employees.length)].employee_id;
      const nameMaterial = faker.commerce.productName();
      const unitPrice = faker.number.float({ min: 0.5, max: 100, precision: 0.01 });
      const category = categories[Math.floor(Math.random() * categories.length)];
      const quantity = faker.number.int({ min: 1, max: 1000 });
      materials.push([
        supplier,
        unitPrice,
        nameMaterial,
        category,
        quantity,
        employee
      ]);
    }
    const sql = `INSERT INTO material_object (supplier_id, unit_price, name_material, category, quantity, employee_id) VALUES ?`;
    await connection.query(sql, [materials]);
    console.log('Inserted 1500 material_object records successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertMaterialObject();
