import React, { useState, useEffect } from 'react';
import { adminAPI } from '../services/api';
import {
    Users,
    UserCheck,
    UserX,
    Shield,
    Activity,
    DollarSign,
    ShoppingCart,
    Package,
    Plus,
    Edit,
    Trash2,
    Unlock,
    Eye,
    AlertTriangle,
    TrendingUp,
    Clock
} from 'lucide-react';

const AdminPage = () => {
    const [activeTab, setActiveTab] = useState('dashboard');
    const [dashboardStats, setDashboardStats] = useState(null);
    const [users, setUsers] = useState([]);
    const [employees, setEmployees] = useState([]);
    const [auditLogs, setAuditLogs] = useState([]);
    const [securityReport, setSecurityReport] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [showCreateUserModal, setShowCreateUserModal] = useState(false);
    const [showCreateEmployeeModal, setShowCreateEmployeeModal] = useState(false);

    // Load data based on active tab
    useEffect(() => {
        loadData();
    }, [activeTab]);

    const loadData = async () => {
        setLoading(true);
        setError(null);
        
        try {
            switch (activeTab) {
                case 'dashboard':
                    const statsResponse = await adminAPI.getDashboardStats();
                    setDashboardStats(statsResponse.data.data);
                    break;
                case 'users':
                    const usersResponse = await adminAPI.getAllUsers();
                    setUsers(usersResponse.data.data);
                    break;
                case 'employees':
                    const employeesResponse = await adminAPI.getAllEmployees();
                    setEmployees(employeesResponse.data.data);
                    break;
                case 'audit':
                    const auditResponse = await adminAPI.getAuditLogs();
                    setAuditLogs(auditResponse.data.data.logs);
                    break;
                case 'security':
                    const securityResponse = await adminAPI.getSecurityReport();
                    setSecurityReport(securityResponse.data.data);
                    break;
            }
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to load data');
        } finally {
            setLoading(false);
        }
    };

    const handleCreateUser = async (userData) => {
        try {
            await adminAPI.createUser(userData);
            setShowCreateUserModal(false);
            loadData(); // Reload users
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to create user');
        }
    };

    const handleCreateEmployee = async (employeeData) => {
        try {
            await adminAPI.createEmployee(employeeData);
            setShowCreateEmployeeModal(false);
            loadData(); // Reload employees
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to create employee');
        }
    };

    const handleUnlockUser = async (username) => {
        try {
            await adminAPI.unlockUser(username);
            loadData(); // Reload users
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to unlock user');
        }
    };

    const handleDeactivateUser = async (username) => {
        if (window.confirm(`Are you sure you want to deactivate user: ${username}?`)) {
            try {
                await adminAPI.deactivateUser(username);
                loadData(); // Reload users
            } catch (err) {
                setError(err.response?.data?.message || 'Failed to deactivate user');
            }
        }
    };

    const handleDeleteEmployee = async (employeeId, employeeName) => {
        if (window.confirm(`Are you sure you want to delete employee: ${employeeName}?`)) {
            try {
                await adminAPI.deleteEmployee(employeeId);
                loadData(); // Reload employees
            } catch (err) {
                setError(err.response?.data?.message || 'Failed to delete employee');
            }
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString();
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    };

    // Dashboard Component
    const Dashboard = () => (
        <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
            
            {dashboardStats && (
                <>
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
                            <div className="flex items-center">
                                <Users className="h-8 w-8 text-blue-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Customers</p>
                                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.counts.customers}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-green-500">
                            <div className="flex items-center">
                                <UserCheck className="h-8 w-8 text-green-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Employees</p>
                                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.counts.employees}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500">
                            <div className="flex items-center">
                                <ShoppingCart className="h-8 w-8 text-purple-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Orders</p>
                                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.counts.orders}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-orange-500">
                            <div className="flex items-center">
                                <Package className="h-8 w-8 text-orange-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Products</p>
                                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.counts.products}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-red-500">
                            <div className="flex items-center">
                                <DollarSign className="h-8 w-8 text-red-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Total Payments</p>
                                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.counts.payments}</p>
                                </div>
                            </div>
                        </div>
                        
                        <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-indigo-500">
                            <div className="flex items-center">
                                <TrendingUp className="h-8 w-8 text-indigo-500" />
                                <div className="ml-4">
                                    <p className="text-sm font-medium text-gray-600">Suppliers</p>
                                    <p className="text-2xl font-bold text-gray-900">{dashboardStats.counts.suppliers}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Recent Activities */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <Activity className="h-5 w-5 mr-2" />
                            Recent Activities
                        </h3>
                        <div className="space-y-3">
                            {dashboardStats.recentActivities.slice(0, 5).map((activity, index) => (
                                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                                    <div className="flex items-center">
                                        <Clock className="h-4 w-4 text-gray-500 mr-2" />
                                        <span className="text-sm text-gray-700">
                                            {activity.user_name} performed {activity.operation_type} on {activity.table_name}
                                        </span>
                                    </div>
                                    <span className="text-xs text-gray-500">
                                        {formatDate(activity.operation_time)}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Revenue Chart (simplified) */}
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Last 30 Days</h3>
                        <div className="space-y-2">
                            {dashboardStats.revenueData.slice(0, 7).map((revenue, index) => (
                                <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                                    <span className="text-sm text-gray-600">{revenue.date}</span>
                                    <span className="text-sm font-medium text-green-600">
                                        {formatCurrency(revenue.revenue)}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                </>
            )}
        </div>
    );

    // Navigation tabs
    const tabs = [
        { id: 'dashboard', label: 'Dashboard', icon: Activity }
    ];

    return (
        <div className="min-h-screen bg-gray-100">
            {/* Header */}
            <div className="bg-white shadow">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center py-6">
                        <h1 className="text-3xl font-bold text-gray-900">Coffee Shop Admin Panel</h1>
                        <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-500">Welcome, Admin</span>
                        </div>
                    </div>
                </div>
            </div>

            {/* Navigation */}
            <div className="bg-white border-b">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <nav className="flex space-x-8">
                        {[
                            { id: 'dashboard', label: 'Dashboard', icon: Activity },
                            { id: 'users', label: 'Users', icon: Users },
                            { id: 'employees', label: 'Employees', icon: UserCheck },
                            { id: 'audit', label: 'Audit Logs', icon: Eye },
                            { id: 'security', label: 'Security', icon: Shield }
                        ].map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center ${
                                    activeTab === tab.id
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                <tab.icon className="h-4 w-4 mr-2" />
                                {tab.label}
                            </button>
                        ))}
                    </nav>
                </div>
            </div>

            {/* Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {error && (
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                        <div className="flex">
                            <AlertTriangle className="h-5 w-5 text-red-400" />
                            <div className="ml-3">
                                <p className="text-sm text-red-800">{error}</p>
                            </div>
                        </div>
                    </div>
                )}

                {loading ? (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                    </div>
                ) : (
                    <>
                        {activeTab === 'dashboard' && <Dashboard />}
                        {activeTab === 'users' && <div>Users section coming soon...</div>}
                        {activeTab === 'employees' && <div>Employees section coming soon...</div>}
                        {activeTab === 'audit' && <div>Audit logs section coming soon...</div>}
                        {activeTab === 'security' && <div>Security section coming soon...</div>}
                    </>
                )}
            </div>
        </div>
    );
};

export default AdminPage;