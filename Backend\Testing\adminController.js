import { connectToAivenDB } from '../src/config/database.js';

// Get all tables and their row counts
export const getDashboardStats = async (req, res) => {
    try {
        const connection = await connectToAivenDB();
        
        // Get counts for all main tables (using correct table names from DDL)
        const [customerCount] = await connection.execute('SELECT COUNT(*) as count FROM customers');
        const [employeeCount] = await connection.execute('SELECT COUNT(*) as count FROM employees');
        const [orderCount] = await connection.execute('SELECT COUNT(*) as count FROM orders');
        const [productCount] = await connection.execute('SELECT COUNT(*) as count FROM products');
        const [supplierCount] = await connection.execute('SELECT COUNT(*) as count FROM suppliers');
        const [paymentCount] = await connection.execute('SELECT COUNT(*) as count FROM payments');
        
        // Get recent activities from audit log
        const [recentActivities] = await connection.execute(`
            SELECT table_name, operation_type, user_name, operation_time 
            FROM audit_log 
            ORDER BY operation_time DESC 
            LIMIT 10
        `);
        
        // Get revenue for last 30 days
        const [revenueData] = await connection.execute(`
            SELECT DATE(p.payment_date) as date, SUM(p.amount_money) as revenue
            FROM payments p
            WHERE p.payment_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(p.payment_date)
            ORDER BY date DESC
        `);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                counts: {
                    customers: customerCount[0].count,
                    employees: employeeCount[0].count,
                    orders: orderCount[0].count,
                    products: productCount[0].count,
                    suppliers: supplierCount[0].count,
                    payments: paymentCount[0].count
                },
                recentActivities,
                revenueData
            }
        });
    } catch (error) {
        console.error('Dashboard stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch dashboard statistics',
            error: error.message
        });
    }
};

// User Management
export const getAllUsers = async (req, res) => {
    try {
        const connection = await connectToAivenDB();
        
        const [users] = await connection.execute(`
            SELECT 
                usc.user_name,
                usc.role_type,
                usc.is_active,
                usc.last_login,
                usc.login_attempts,
                usc.locked_until,
                CASE 
                    WHEN usc.locked_until IS NOT NULL AND usc.locked_until > NOW() THEN 'LOCKED'
                    WHEN NOT usc.is_active THEN 'INACTIVE'
                    ELSE 'ACTIVE'
                END AS account_status,
                CONCAT(e.first_name, ' ', e.last_name) AS employee_name,
                e.position AS employee_position
            FROM user_security_context usc
            LEFT JOIN employees e ON usc.employee_id = e.employee_id
            ORDER BY usc.user_name
        `);
        
        connection.release();
        
        res.json({
            success: true,
            data: users
        });
    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users',
            error: error.message
        });
    }
};

export const createUser = async (req, res) => {
    try {
        const { username, password, employeeId, roleType } = req.body;
        
        if (!username || !password || !roleType) {
            return res.status(400).json({
                success: false,
                message: 'Username, password, and role type are required'
            });
        }
        
        const connection = await connectToAivenDB();
        
        // Call stored procedure to create user
        const [result] = await connection.execute(
            'CALL create_employee_user(?, ?, ?, ?, @result)',
            [username, password, employeeId, roleType]
        );
        
        const [resultMessage] = await connection.execute('SELECT @result as message');
        
        connection.release();
        
        res.json({
            success: true,
            message: resultMessage[0].message
        });
    } catch (error) {
        console.error('Create user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create user',
            error: error.message
        });
    }
};

export const deactivateUser = async (req, res) => {
    try {
        const { username } = req.params;
        
        const connection = await connectToAivenDB();
        
        // Call stored procedure to deactivate user
        const [result] = await connection.execute(
            'CALL deactivate_user(?, @result)',
            [username]
        );
        
        const [resultMessage] = await connection.execute('SELECT @result as message');
        
        connection.release();
        
        res.json({
            success: true,
            message: resultMessage[0].message
        });
    } catch (error) {
        console.error('Deactivate user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to deactivate user',
            error: error.message
        });
    }
};

export const unlockUser = async (req, res) => {
    try {
        const { username } = req.params;
        
        const connection = await connectToAivenDB();
        
        // Call stored procedure to unlock user
        const [result] = await connection.execute(
            'CALL unlock_user_account(?, @result)',
            [username]
        );
        
        const [resultMessage] = await connection.execute('SELECT @result as message');
        
        connection.release();
        
        res.json({
            success: true,
            message: resultMessage[0].message
        });
    } catch (error) {
        console.error('Unlock user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to unlock user',
            error: error.message
        });
    }
};

// Employee Management
export const getAllEmployees = async (req, res) => {
    try {
        const connection = await connectToAivenDB();
        
        const [employees] = await connection.execute(`
            SELECT 
                employee_id,
                first_name,
                last_name,
                position,
                hire_date,
                salary,
                phone_number,
                email,
                address
            FROM employees
            ORDER BY hire_date DESC
        `);
        
        connection.release();
        
        res.json({
            success: true,
            data: employees
        });
    } catch (error) {
        console.error('Get employees error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch employees',
            error: error.message
        });
    }
};

export const createEmployee = async (req, res) => {
    try {
        const {
            firstName,
            lastName,
            position,
            hireDate,
            salary,
            phoneNumber,
            email,
            address
        } = req.body;
        
        if (!firstName || !lastName || !position || !hireDate || !salary || !phoneNumber || !email) {
            return res.status(400).json({
                success: false,
                message: 'All required fields must be provided'
            });
        }
        
        const connection = await connectToAivenDB();
        
        const [result] = await connection.execute(`
            INSERT INTO employees (first_name, last_name, position, hire_date, salary, phone_number, email, address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [firstName, lastName, position, hireDate, salary, phoneNumber, email, address]);
        
        connection.release();
        
        res.json({
            success: true,
            message: 'Employee created successfully',
            data: { employeeId: result.insertId }
        });
    } catch (error) {
        console.error('Create employee error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create employee',
            error: error.message
        });
    }
};

export const updateEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        const {
            firstName,
            lastName,
            position,
            salary,
            phoneNumber,
            email,
            address
        } = req.body;
        
        const connection = await connectToAivenDB();
        
        const [result] = await connection.execute(`
            UPDATE employees 
            SET first_name = ?, last_name = ?, position = ?, salary = ?, 
                phone_number = ?, email = ?, address = ?
            WHERE employee_id = ?
        `, [firstName, lastName, position, salary, phoneNumber, email, address, id]);
        
        connection.release();
        
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found'
            });
        }
        
        res.json({
            success: true,
            message: 'Employee updated successfully'
        });
    } catch (error) {
        console.error('Update employee error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update employee',
            error: error.message
        });
    }
};

export const deleteEmployee = async (req, res) => {
    try {
        const { id } = req.params;
        
        const connection = await connectToAivenDB();
        
        const [result] = await connection.execute(
            'DELETE FROM employees WHERE employee_id = ?',
            [id]
        );
        
        connection.end();
        
        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                message: 'Employee not found'
            });
        }
        
        res.json({
            success: true,
            message: 'Employee deleted successfully'
        });
    } catch (error) {
        console.error('Delete employee error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete employee',
            error: error.message
        });
    }
};

// Audit Log
export const getAuditLogs = async (req, res) => {
    try {
        const { limit = 50, offset = 0 } = req.query;
        
        const connection = await connectToAivenDB();
        
        const [logs] = await connection.execute(`
            SELECT 
                log_id,
                table_name,
                operation_type,
                user_name,
                operation_time,
                old_values,
                new_values
            FROM audit_log
            ORDER BY operation_time DESC
            LIMIT ? OFFSET ?
        `, [parseInt(limit), parseInt(offset)]);
        
        const [totalCount] = await connection.execute('SELECT COUNT(*) as count FROM audit_log');
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                logs,
                total: totalCount[0].count,
                limit: parseInt(limit),
                offset: parseInt(offset)
            }
        });
    } catch (error) {
        console.error('Get audit logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch audit logs',
            error: error.message
        });
    }
};

// Security Reports
export const getSecurityReport = async (req, res) => {
    try {
        const connection = await connectToAivenDB();
        
        // Call stored procedure to generate security report
        await connection.execute('CALL generate_security_report()');
        
        // Get the results (this is a simplified version - in practice you'd need to handle multiple result sets)
        const [userStatusSummary] = await connection.execute(`
            SELECT 
                CASE 
                    WHEN locked_until IS NOT NULL AND locked_until > NOW() THEN 'LOCKED'
                    WHEN NOT is_active THEN 'INACTIVE'
                    ELSE 'ACTIVE'
                END AS account_status,
                COUNT(*) AS user_count
            FROM user_security_context
            GROUP BY account_status
        `);
        
        const [recentAuditActivity] = await connection.execute(`
            SELECT 
                DATE(operation_time) AS audit_date,
                table_name,
                operation_type,
                COUNT(*) AS operation_count
            FROM audit_log
            WHERE operation_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(operation_time), table_name, operation_type
            ORDER BY audit_date DESC, operation_count DESC
            LIMIT 20
        `);
        
        const [highRiskUsers] = await connection.execute(`
            SELECT 
                user_name,
                role_type,
                login_attempts,
                CASE 
                    WHEN login_attempts >= 3 THEN 'HIGH RISK'
                    WHEN login_attempts >= 2 THEN 'MEDIUM RISK'
                    WHEN login_attempts >= 1 THEN 'LOW RISK'
                    ELSE 'NO RISK'
                END AS risk_level,
                locked_until
            FROM user_security_context
            WHERE login_attempts > 0 OR locked_until IS NOT NULL
            ORDER BY login_attempts DESC, locked_until DESC
        `);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                userStatusSummary,
                recentAuditActivity,
                highRiskUsers
            }
        });
    } catch (error) {
        console.error('Security report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate security report',
            error: error.message
        });
    }
};

// Activity Logging Management
export const getActivityLogs = async (req, res) => {
    try {
        const { 
            limit = 50, 
            offset = 0, 
            startDate, 
            endDate, 
            ipAddress, 
            method, 
            statusCode 
        } = req.query;
        
        const connection = await connectToAivenDB();
        
        // Build dynamic query
        let whereConditions = [];
        let queryParams = [];
        
        if (startDate) {
            whereConditions.push('timestamp >= ?');
            queryParams.push(startDate);
        }
        
        if (endDate) {
            whereConditions.push('timestamp <= ?');
            queryParams.push(endDate);
        }
        
        if (ipAddress) {
            whereConditions.push('ip_address = ?');
            queryParams.push(ipAddress);
        }
        
        if (method) {
            whereConditions.push('method = ?');
            queryParams.push(method);
        }
        
        if (statusCode) {
            whereConditions.push('status_code = ?');
            queryParams.push(parseInt(statusCode));
        }
        
        const whereClause = whereConditions.length > 0 ? 
            'WHERE ' + whereConditions.join(' AND ') : '';
        
        const [logs] = await connection.execute(`
            SELECT 
                log_id,
                timestamp,
                method,
                url,
                path,
                ip_address,
                status_code,
                response_time_ms,
                success,
                error_message,
                user_id
            FROM activity_log 
            ${whereClause}
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        `, [...queryParams, parseInt(limit), parseInt(offset)]);
        
        const [totalCount] = await connection.execute(`
            SELECT COUNT(*) as count FROM activity_log ${whereClause}
        `, queryParams);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                logs,
                total: totalCount[0].count,
                limit: parseInt(limit),
                offset: parseInt(offset)
            }
        });
    } catch (error) {
        console.error('Get activity logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch activity logs',
            error: error.message
        });
    }
};

export const getSecurityLogs = async (req, res) => {
    try {
        const { limit = 50, offset = 0, severity } = req.query;
        
        const connection = await connectToAivenDB();
        
        let whereClause = '';
        let queryParams = [];
        
        if (severity) {
            whereClause = 'WHERE severity = ?';
            queryParams.push(severity);
        }
        
        const [logs] = await connection.execute(`
            SELECT 
                log_id,
                timestamp,
                ip_address,
                method,
                url,
                detected_patterns,
                severity,
                blocked
            FROM security_log 
            ${whereClause}
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        `, [...queryParams, parseInt(limit), parseInt(offset)]);
        
        const [totalCount] = await connection.execute(`
            SELECT COUNT(*) as count FROM security_log ${whereClause}
        `, queryParams);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                logs,
                total: totalCount[0].count,
                limit: parseInt(limit),
                offset: parseInt(offset)
            }
        });
    } catch (error) {
        console.error('Get security logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch security logs',
            error: error.message
        });
    }
};

export const getApiUsageStats = async (req, res) => {
    try {
        const { days = 7 } = req.query;
        
        const connection = await connectToAivenDB();
        
        // Get API usage statistics
        const [usageStats] = await connection.execute(`
            SELECT * FROM api_usage_stats
            LIMIT 20
        `);
        
        // Get daily activity summary
        const [dailyStats] = await connection.execute(`
            SELECT * FROM daily_activity_summary
            WHERE activity_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            ORDER BY activity_date DESC
        `, [parseInt(days)]);
        
        // Get top IPs by request count
        const [topIPs] = await connection.execute(`
            SELECT 
                ip_address,
                COUNT(*) as request_count,
                AVG(response_time_ms) as avg_response_time,
                MAX(timestamp) as last_request
            FROM activity_log 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY ip_address
            ORDER BY request_count DESC
            LIMIT 10
        `, [parseInt(days)]);
        
        // Get error rate by endpoint
        const [errorStats] = await connection.execute(`
            SELECT 
                path,
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as error_count,
                ROUND((SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as error_rate
            FROM activity_log 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
            GROUP BY path
            HAVING error_count > 0
            ORDER BY error_rate DESC
            LIMIT 10
        `, [parseInt(days)]);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                usageStats,
                dailyStats,
                topIPs,
                errorStats
            }
        });
    } catch (error) {
        console.error('Get API usage stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch API usage statistics',
            error: error.message
        });
    }
};

export const getSystemMetrics = async (req, res) => {
    try {
        const connection = await connectToAivenDB();
        
        // Get real-time metrics
        const [recentActivity] = await connection.execute(`
            SELECT 
                COUNT(*) as requests_last_hour,
                AVG(response_time_ms) as avg_response_time,
                COUNT(DISTINCT ip_address) as unique_ips
            FROM activity_log 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        `);
        
        const [securityThreats] = await connection.execute(`
            SELECT 
                COUNT(*) as threats_last_24h,
                COUNT(DISTINCT ip_address) as threat_sources
            FROM security_log 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `);
        
        const [systemHealth] = await connection.execute(`
            SELECT 
                SUM(CASE WHEN status_code >= 500 THEN 1 ELSE 0 END) as server_errors,
                SUM(CASE WHEN status_code >= 400 AND status_code < 500 THEN 1 ELSE 0 END) as client_errors,
                SUM(CASE WHEN status_code >= 200 AND status_code < 300 THEN 1 ELSE 0 END) as successful_requests
            FROM activity_log 
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `);
        
        connection.release();
        
        res.json({
            success: true,
            data: {
                recentActivity: recentActivity[0],
                securityThreats: securityThreats[0],
                systemHealth: systemHealth[0],
                timestamp: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('Get system metrics error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch system metrics',
            error: error.message
        });
    }
};

export const cleanOldLogs = async (req, res) => {
    try {
        const { daysToKeep = 90 } = req.body;
        
        if (daysToKeep < 30) {
            return res.status(400).json({
                success: false,
                message: 'Cannot keep logs for less than 30 days'
            });
        }
        
        const connection = await connectToAivenDB();
        
        // Call stored procedure to clean old logs
        await connection.execute('CALL CleanOldLogs(?)', [parseInt(daysToKeep)]);
        
        connection.release();
        
        res.json({
            success: true,
            message: `Successfully cleaned logs older than ${daysToKeep} days`
        });
    } catch (error) {
        console.error('Clean old logs error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to clean old logs',
            error: error.message
        });
    }
};
