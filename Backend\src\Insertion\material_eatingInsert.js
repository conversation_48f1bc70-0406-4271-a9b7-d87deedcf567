
import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertMaterialEating() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Get supplier and employee IDs for foreign keys
    const [suppliers] = await connection.query('SELECT supplier_id FROM suppliers');
    const [employees] = await connection.query('SELECT employee_id FROM employees');
    console.log('Suppliers found:', suppliers.length, 'Employees found:', employees.length);
    if (suppliers.length === 0 || employees.length === 0) {
      throw new Error('Suppliers or Employees table is empty. Please insert data first.');
    }
    // const categories = [
    //   'caffee been', 'matcha', 'sugar', 'condensed milk', 'fresh milk', 'espresso',
    //   'chocolate', 'green tea', 'black tea', 'caramel syrup', 'vanilla syrup',
    //   'hazelnut syrup', 'ice', 'whipped cream', 'honey', 'almond milk', 'soy milk',
    //   'coconut milk', 'mint', 'lemon juice'
    // ];
    const categories = [
      'coffee bean',
	  'matcha',
	  'sugar',
	  'condensed milk',
	  'fresh milk',
	  'espresso',
	  'chocolate',
	  'green tea',
	  'black tea',
	  'caramel syrup',
	  'vanilla syrup',
	  'hazelnut syrup',
	  'ice',
	  'whipped cream',
	  'honey',
	  'almond milk',
	  'soy milk',
	  'coconut milk',
	  'mint',
	  'lemon juice'
    ];
    const materials = [];
    for (let i = 0; i < 1500; i++) {
      const supplier = suppliers[Math.floor(Math.random() * suppliers.length)].supplier_id;
      const employee = employees[Math.floor(Math.random() * employees.length)].employee_id;
      const nameMaterial = faker.commerce.productName();
      const quantity = faker.number.int({ min: 1, max: 1000 });
      const unitPrice = faker.number.float({ min: 0.5, max: 100, precision: 0.01 });
      const category = categories[Math.floor(Math.random() * categories.length)];
      materials.push([
        supplier,
        nameMaterial,
        quantity,
        unitPrice,
        category,
        employee
      ]);
    }
    const sql = `INSERT INTO material_eating (supplier_id, name_material, quantity, unit_price, category, employee_id) VALUES ?`;
    await connection.query(sql, [materials]);
    console.log('Inserted 1500 material_eating records successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertMaterialEating();
