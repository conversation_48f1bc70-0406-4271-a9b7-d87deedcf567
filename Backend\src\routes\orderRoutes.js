import express from 'express';
import OrderController from '../controllers/orderController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Order:
 *       type: object
 *       required:
 *         - product_id
 *         - quantity
 *         - unit_price
 *       properties:
 *         order_id:
 *           type: integer
 *           description: Auto-generated order ID
 *         customer_id:
 *           type: integer
 *           nullable: true
 *           description: Reference to customer (nullable for walk-in customers)
 *         product_id:
 *           type: integer
 *           description: Reference to product
 *         order_date:
 *           type: string
 *           format: date-time
 *           description: Order timestamp
 *         quantity:
 *           type: integer
 *           minimum: 1
 *           description: Order quantity
 *         unit_price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *           description: Unit price at time of order
 *         status_order:
 *           type: string
 *           enum: [Pending, Completed]
 *           default: Pending
 *           description: Order status
 *         customer_name:
 *           type: string
 *           nullable: true
 *           description: Customer name (read-only)
 *         product_name:
 *           type: string
 *           description: Product name (read-only)
 *         total_amount:
 *           type: number
 *           description: Total amount (quantity * unit_price) (read-only)
 *       example:
 *         order_id: 1
 *         customer_id: 1
 *         product_id: 1
 *         order_date: "2024-01-15T10:30:00Z"
 *         quantity: 2
 *         unit_price: 4.50
 *         status_order: "Pending"
 *         customer_name: "<PERSON>"
 *         product_name: "Cappuccino"
 *         total_amount: 9.00
 *
 *     OrderInput:
 *       type: object
 *       required:
 *         - product_id
 *         - quantity
 *         - unit_price
 *       properties:
 *         customer_id:
 *           type: integer
 *           nullable: true
 *         product_id:
 *           type: integer
 *         quantity:
 *           type: integer
 *           minimum: 1
 *         unit_price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *       example:
 *         customer_id: 1
 *         product_id: 1
 *         quantity: 2
 *         unit_price: 4.50
 */

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get all orders with pagination and filtering
 *     tags: [Orders]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of orders per page
 *       - in: query
 *         name: customer_id
 *         schema:
 *           type: integer
 *         description: Filter by customer ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Pending, Completed]
 *         description: Filter by order status
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter orders from this date
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter orders to this date
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Order'
 *                 pagination:
 *                   type: object
 */
router.get('/', OrderController.getAllOrders);

/**
 * @swagger
 * /api/orders/unpaid:
 *   get:
 *     summary: Get orders without payment
 *     tags: [Orders]
 *     responses:
 *       200:
 *         description: Orders without payment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       order_id:
 *                         type: integer
 *                       customer_name:
 *                         type: string
 *                       product_name:
 *                         type: string
 *                       quantity:
 *                         type: integer
 *                       unit_price:
 *                         type: number
 *                       total_amount:
 *                         type: number
 *                       order_date:
 *                         type: string
 *                         format: date-time
 */
router.get('/unpaid', OrderController.getOrdersWithoutPayment);

/**
 * @swagger
 * /api/orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 *       404:
 *         description: Order not found
 */
router.get('/:id', OrderController.getOrderById);

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: Create a new order
 *     tags: [Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OrderInput'
 *     responses:
 *       201:
 *         description: Order created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Customer or product not found
 */
router.post('/', OrderController.createOrder);

/**
 * @swagger
 * /api/orders/{id}/status:
 *   put:
 *     summary: Update order status
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status_order
 *             properties:
 *               status_order:
 *                 type: string
 *                 enum: [Pending, Completed]
 *     responses:
 *       200:
 *         description: Order status updated successfully
 *       400:
 *         description: Invalid status
 *       404:
 *         description: Order not found
 */
router.put('/:id/status', OrderController.updateOrderStatus);

/**
 * @swagger
 * /api/orders/{id}:
 *   delete:
 *     summary: Delete order (only if no payment exists)
 *     tags: [Orders]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order deleted successfully
 *       404:
 *         description: Order not found
 *       409:
 *         description: Cannot delete order with existing payment
 */
router.delete('/:id', OrderController.deleteOrder);

export default router;
