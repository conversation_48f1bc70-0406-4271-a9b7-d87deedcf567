import DatabaseService from '../services/databaseService.js';

/**
 * Product Controller
 * Handles all product-related operations
 */

class ProductController {
    /**
     * Get all products with pagination and filtering
     */
    static async getAllProducts(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search || '';
            const category = req.query.category || '';

            let baseQuery = `
                SELECT 
                    p.product_id,
                    p.product_name,
                    p.category_name,
                    p.description,
                    p.unit_price,
                    me.name_material as eating_material,
                    mo.name_material as object_material
                FROM products p
                LEFT JOIN material_eating me ON p.material_eating_id = me.material_eating_id
                LEFT JOIN material_object mo ON p.material_object_id = mo.material_object_id
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (search) {
                whereConditions.push(`(
                    p.product_name LIKE ? OR 
                    p.description LIKE ?
                )`);
                const searchParam = `%${search}%`;
                params.push(searchParam, searchParam);
            }
            
            if (category) {
                whereConditions.push('p.category_name = ?');
                params.push(category);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY p.product_name ASC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Products retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting products:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve products',
                error: error.message
            });
        }
    }

    /**
     * Get product by ID
     */
    static async getProductById(req, res) {
        try {
            const { id } = req.params;
            
            const product = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.product_id,
                    p.product_name,
                    p.category_name,
                    p.description,
                    p.unit_price,
                    me.name_material as eating_material,
                    me.quantity as eating_quantity,
                    mo.name_material as object_material,
                    mo.quantity as object_quantity
                FROM products p
                LEFT JOIN material_eating me ON p.material_eating_id = me.material_eating_id
                LEFT JOIN material_object mo ON p.material_object_id = mo.material_object_id
                WHERE p.product_id = ?`,
                [id]
            );

            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
            }

            res.json({
                success: true,
                message: 'Product retrieved successfully',
                data: product
            });
        } catch (error) {
            console.error('Error getting product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve product',
                error: error.message
            });
        }
    }

    /**
     * Create new product
     */
    static async createProduct(req, res) {
        try {
            const { 
                product_name, 
                material_eating_id, 
                material_object_id, 
                category_name, 
                description, 
                unit_price 
            } = req.body;

            // Validation
            if (!product_name || !category_name || !unit_price) {
                return res.status(400).json({
                    success: false,
                    message: 'Required fields: product_name, category_name, unit_price'
                });
            }

            // Validate category enum
            const validCategories = ['Coffee', 'Tea', 'Pastry', 'Sandwich', 'Juice', 'Other'];
            if (!validCategories.includes(category_name)) {
                return res.status(400).json({
                    success: false,
                    message: `Category must be one of: ${validCategories.join(', ')}`
                });
            }

            // Validate unit_price is positive
            if (unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Unit price must be greater than 0'
                });
            }

            // Check if material references exist
            if (material_eating_id) {
                const eatingExists = await DatabaseService.recordExists('material_eating', 'material_eating_id', material_eating_id);
                if (!eatingExists) {
                    return res.status(404).json({
                        success: false,
                        message: 'Material eating not found'
                    });
                }
            }

            if (material_object_id) {
                const objectExists = await DatabaseService.recordExists('material_object', 'material_object_id', material_object_id);
                if (!objectExists) {
                    return res.status(404).json({
                        success: false,
                        message: 'Material object not found'
                    });
                }
            }

            const productId = await DatabaseService.executeInsert(
                `INSERT INTO products (product_name, material_eating_id, material_object_id, category_name, description, unit_price) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [product_name, material_eating_id || null, material_object_id || null, category_name, description || null, unit_price]
            );

            const newProduct = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.product_id,
                    p.product_name,
                    p.category_name,
                    p.description,
                    p.unit_price,
                    me.name_material as eating_material,
                    mo.name_material as object_material
                FROM products p
                LEFT JOIN material_eating me ON p.material_eating_id = me.material_eating_id
                LEFT JOIN material_object mo ON p.material_object_id = mo.material_object_id
                WHERE p.product_id = ?`,
                [productId]
            );

            res.status(201).json({
                success: true,
                message: 'Product created successfully',
                data: newProduct
            });
        } catch (error) {
            console.error('Error creating product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create product',
                error: error.message
            });
        }
    }

    /**
     * Update product
     */
    static async updateProduct(req, res) {
        try {
            const { id } = req.params;
            const { 
                product_name, 
                material_eating_id, 
                material_object_id, 
                category_name, 
                description, 
                unit_price 
            } = req.body;

            // Check if product exists
            const existingProduct = await DatabaseService.recordExists('products', 'product_id', id);
            if (!existingProduct) {
                return res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
            }

            // Validate category enum if provided
            if (category_name) {
                const validCategories = ['Coffee', 'Tea', 'Pastry', 'Sandwich', 'Juice', 'Other'];
                if (!validCategories.includes(category_name)) {
                    return res.status(400).json({
                        success: false,
                        message: `Category must be one of: ${validCategories.join(', ')}`
                    });
                }
            }

            // Validate unit_price if provided
            if (unit_price !== undefined && unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Unit price must be greater than 0'
                });
            }

            // Check if material references exist
            if (material_eating_id) {
                const eatingExists = await DatabaseService.recordExists('material_eating', 'material_eating_id', material_eating_id);
                if (!eatingExists) {
                    return res.status(404).json({
                        success: false,
                        message: 'Material eating not found'
                    });
                }
            }

            if (material_object_id) {
                const objectExists = await DatabaseService.recordExists('material_object', 'material_object_id', material_object_id);
                if (!objectExists) {
                    return res.status(404).json({
                        success: false,
                        message: 'Material object not found'
                    });
                }
            }

            const affectedRows = await DatabaseService.executeUpdate(
                `UPDATE products 
                 SET product_name = COALESCE(?, product_name),
                     material_eating_id = ?,
                     material_object_id = ?,
                     category_name = COALESCE(?, category_name),
                     description = ?,
                     unit_price = COALESCE(?, unit_price)
                 WHERE product_id = ?`,
                [product_name, material_eating_id || null, material_object_id || null, category_name, description, unit_price, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Product not found or no changes made'
                });
            }

            const updatedProduct = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.product_id,
                    p.product_name,
                    p.category_name,
                    p.description,
                    p.unit_price,
                    me.name_material as eating_material,
                    mo.name_material as object_material
                FROM products p
                LEFT JOIN material_eating me ON p.material_eating_id = me.material_eating_id
                LEFT JOIN material_object mo ON p.material_object_id = mo.material_object_id
                WHERE p.product_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Product updated successfully',
                data: updatedProduct
            });
        } catch (error) {
            console.error('Error updating product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update product',
                error: error.message
            });
        }
    }

    /**
     * Delete product
     */
    static async deleteProduct(req, res) {
        try {
            const { id } = req.params;

            // Check if product has orders
            const hasOrders = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM orders WHERE product_id = ? LIMIT 1',
                [id]
            );

            if (hasOrders) {
                return res.status(409).json({
                    success: false,
                    message: 'Cannot delete product with existing orders'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                'DELETE FROM products WHERE product_id = ?',
                [id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Product not found'
                });
            }

            res.json({
                success: true,
                message: 'Product deleted successfully'
            });
        } catch (error) {
            console.error('Error deleting product:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete product',
                error: error.message
            });
        }
    }

    /**
     * Get products by category
     */
    static async getProductsByCategory(req, res) {
        try {
            const { category } = req.params;
            
            const validCategories = ['Coffee', 'Tea', 'Pastry', 'Sandwich', 'Juice', 'Other'];
            if (!validCategories.includes(category)) {
                return res.status(400).json({
                    success: false,
                    message: `Category must be one of: ${validCategories.join(', ')}`
                });
            }

            const products = await DatabaseService.executeQuery(
                `SELECT 
                    p.product_id,
                    p.product_name,
                    p.category_name,
                    p.description,
                    p.unit_price,
                    me.name_material as eating_material,
                    mo.name_material as object_material
                FROM products p
                LEFT JOIN material_eating me ON p.material_eating_id = me.material_eating_id
                LEFT JOIN material_object mo ON p.material_object_id = mo.material_object_id
                WHERE p.category_name = ?
                ORDER BY p.product_name ASC`,
                [category]
            );

            res.json({
                success: true,
                message: `${category} products retrieved successfully`,
                data: products
            });
        } catch (error) {
            console.error('Error getting products by category:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve products by category',
                error: error.message
            });
        }
    }
}

export default ProductController;
