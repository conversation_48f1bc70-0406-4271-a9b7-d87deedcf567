import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    Coffee, ShoppingBag, DollarSign, Activity, LogOut, Menu, X,
    TrendingUp, Clock, Star, BarChart3, Eye, User
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissions } from '../auth/RoleBasedAccess';
import { coffeeAPI } from '../services/api';

const UserDashboard = () => {
    const { user, logout } = useAuth();
    const permissions = usePermissions();
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [activeTab, setActiveTab] = useState('dashboard');
    const [data, setData] = useState({
        orders: [],
        analytics: {},
        loading: true
    });

    // Define navigation items based on user role
    const getNavigationItems = () => {
        const baseItems = [
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3 }
        ];

        if (permissions.canProcessOrders()) {
            baseItems.push({ id: 'orders', label: 'Orders', icon: ShoppingBag });
        }

        if (permissions.canViewInventory()) {
            baseItems.push({ id: 'inventory', label: 'Inventory', icon: Coffee });
        }

        if (permissions.canViewReports()) {
            baseItems.push({ id: 'reports', label: 'Reports', icon: TrendingUp });
        }

        return baseItems;
    };

    const navigationItems = getNavigationItems();

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            setData(prev => ({ ...prev, loading: true }));
            
            // Fetch data based on user permissions
            const promises = [];
            
            if (permissions.canProcessOrders()) {
                promises.push(coffeeAPI.getOrders());
            }
            
            if (permissions.canViewReports()) {
                promises.push(coffeeAPI.getAnalytics());
            }

            const results = await Promise.allSettled(promises);
            
            setData({
                orders: results[0]?.status === 'fulfilled' ? results[0].value.data : [],
                analytics: results[1]?.status === 'fulfilled' ? results[1].value.data : {},
                loading: false
            });
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
            setData(prev => ({ ...prev, loading: false }));
        }
    };

    const handleLogout = async () => {
        await logout();
    };

    return (
        <div className="min-h-screen bg-gray-50 flex">
            {/* Sidebar */}
            <motion.div
                initial={{ x: -300 }}
                animate={{ x: sidebarOpen ? 0 : -300 }}
                transition={{ duration: 0.3 }}
                className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg lg:relative lg:translate-x-0"
            >
                <div className="flex items-center justify-between h-16 px-6 border-b">
                    <div className="flex items-center space-x-2">
                        <Coffee className="w-8 h-8 text-emerald-600" />
                        <span className="text-xl font-bold text-gray-800">Coffee System</span>
                    </div>
                    <button
                        onClick={() => setSidebarOpen(false)}
                        className="lg:hidden p-2 rounded-md hover:bg-gray-100"
                    >
                        <X className="w-5 h-5" />
                    </button>
                </div>

                <nav className="mt-6">
                    {navigationItems.map((item) => {
                        const Icon = item.icon;
                        return (
                            <button
                                key={item.id}
                                onClick={() => {
                                    setActiveTab(item.id);
                                    setSidebarOpen(false);
                                }}
                                className={`w-full flex items-center px-6 py-3 text-left hover:bg-emerald-50 transition-colors ${
                                    activeTab === item.id
                                        ? 'bg-emerald-50 text-emerald-600 border-r-2 border-emerald-600'
                                        : 'text-gray-600'
                                }`}
                            >
                                <Icon className="w-5 h-5 mr-3" />
                                {item.label}
                            </button>
                        );
                    })}
                </nav>

                <div className="absolute bottom-0 w-full p-6 border-t">
                    <div className="flex items-center space-x-3 mb-4">
                        <div className="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                            <User className="w-5 h-5 text-emerald-600" />
                        </div>
                        <div>
                            <p className="font-medium text-gray-800">{user?.name || user?.username}</p>
                            <p className="text-sm text-gray-500 capitalize">{user?.role}</p>
                        </div>
                    </div>
                    <button
                        onClick={handleLogout}
                        className="w-full flex items-center px-4 py-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    >
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                    </button>
                </div>
            </motion.div>

            {/* Main Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
                {/* Header */}
                <header className="bg-white shadow-sm border-b h-16 flex items-center justify-between px-6">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => setSidebarOpen(true)}
                            className="lg:hidden p-2 rounded-md hover:bg-gray-100"
                        >
                            <Menu className="w-5 h-5" />
                        </button>
                        <h1 className="text-xl font-semibold text-gray-800 capitalize">
                            {activeTab}
                        </h1>
                    </div>
                    <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-500">
                            Welcome, {user?.name || user?.username}
                        </span>
                    </div>
                </header>

                {/* Content */}
                <main className="flex-1 overflow-auto p-6">
                    <UserDashboardContent activeTab={activeTab} data={data} permissions={permissions} />
                </main>
            </div>

            {/* Overlay for mobile */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
};

// Dashboard Content Component
const UserDashboardContent = ({ activeTab, data, permissions }) => {
    if (activeTab === 'dashboard') {
        return <UserDashboardOverview data={data} permissions={permissions} />;
    }

    return (
        <div className="text-center py-20">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Coffee className="w-8 h-8 text-emerald-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Section
            </h3>
            <p className="text-gray-600">
                {permissions.canViewReports() 
                    ? "This section is coming soon!" 
                    : "You don't have permission to access this section."
                }
            </p>
        </div>
    );
};

// User Dashboard Overview Component
const UserDashboardOverview = ({ data, permissions }) => {
    const { loading } = data;

    if (loading) {
        return (
            <div className="flex items-center justify-center py-20">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* Stats Cards - shown based on permissions */}
                {permissions.canProcessOrders() && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="bg-white p-6 rounded-lg shadow-sm border"
                    >
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Today's Orders</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {data.orders?.length || 0}
                                </p>
                            </div>
                            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <ShoppingBag className="w-6 h-6 text-blue-600" />
                            </div>
                        </div>
                    </motion.div>
                )}

                {permissions.canViewReports() && (
                    <>
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.1 }}
                            className="bg-white p-6 rounded-lg shadow-sm border"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Revenue</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        ${data.analytics?.revenue || '0.00'}
                                    </p>
                                </div>
                                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <DollarSign className="w-6 h-6 text-green-600" />
                                </div>
                            </div>
                        </motion.div>

                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                            className="bg-white p-6 rounded-lg shadow-sm border"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Active Users</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {data.analytics?.activeUsers || 0}
                                    </p>
                                </div>
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <Activity className="w-6 h-6 text-purple-600" />
                                </div>
                            </div>
                        </motion.div>
                    </>
                )}

                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-white p-6 rounded-lg shadow-sm border"
                >
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm font-medium text-gray-600">Your Access Level</p>
                            <p className="text-2xl font-bold text-gray-900 capitalize">
                                {permissions.isAdmin() ? 'Full Access' : 
                                 permissions.isManager() ? 'Manager' :
                                 permissions.isCashier() ? 'Cashier' : 'Limited'}
                            </p>
                        </div>
                        <div className="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center">
                            <Star className="w-6 h-6 text-emerald-600" />
                        </div>
                    </div>
                </motion.div>
            </div>

            {/* Welcome Message */}
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white p-6 rounded-lg shadow-sm border"
            >
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    Welcome to Coffee Management System
                </h3>
                <p className="text-gray-600">
                    You have {permissions.isAdmin() ? 'administrator' : permissions.isManager() ? 'manager' : permissions.isCashier() ? 'cashier' : 'user'} access. 
                    Use the navigation menu to access the features available to your role.
                </p>
            </motion.div>
        </div>
    );
};

export default UserDashboard;
