import express from 'express';
import {
    getDashboardStats,
    getAllUsers,
    createUser,
    deactivateUser,
    unlockUser,
    getAllEmployees,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    getAuditLogs,
    getSecurityReport,
    getActivityLogs,
    getSecurityLogs,
    getApiUsageStats,
    getSystemMetrics,
    cleanOldLogs
} from './adminController.js';
import { authenticateToken, requireAdmin, requireAdminOrManager } from '../middleware/authMiddleware.js';

const router = express.Router();

// Apply authentication to all admin routes
router.use(authenticateToken);

/**
 * @swagger
 * /admin/dashboard/stats:
 *   get:
 *     summary: Get dashboard statistics
 *     description: Retrieve comprehensive dashboard statistics including customer counts, orders, revenue data, and recent activities
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DashboardStats'
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Access denied - insufficient permissions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/dashboard/stats', requireAdminOrManager, getDashboardStats);

/**
 * @swagger
 * /admin/users:
 *   get:
 *     summary: Get all users
 *     description: Retrieve a list of all users in the system with their roles, status, and employee information
 *     tags: [User Management]
 *     responses:
 *       200:
 *         description: Users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/users', requireAdmin, getAllUsers);

/**
 * @swagger
 * /admin/users:
 *   post:
 *     summary: Create a new user
 *     description: Create a new user account with specified role and employee association
 *     tags: [User Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateUserRequest'
 *     responses:
 *       201:
 *         description: User created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/users', requireAdmin, createUser);

/**
 * @swagger
 * /admin/users/{username}/deactivate:
 *   patch:
 *     summary: Deactivate a user account
 *     description: Deactivate a user account by username
 *     tags: [User Management]
 *     parameters:
 *       - in: path
 *         name: username
 *         required: true
 *         schema:
 *           type: string
 *         description: Username of the account to deactivate
 *         example: user123
 *     responses:
 *       200:
 *         description: User deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.patch('/users/:username/deactivate', requireAdmin, deactivateUser);

/**
 * @swagger
 * /admin/users/{username}/unlock:
 *   patch:
 *     summary: Unlock a user account
 *     description: Unlock a locked user account and reset failed login attempts
 *     tags: [User Management]
 *     parameters:
 *       - in: path
 *         name: username
 *         required: true
 *         schema:
 *           type: string
 *         description: Username of the account to unlock
 *         example: user123
 *     responses:
 *       200:
 *         description: User unlocked successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.patch('/users/:username/unlock', requireAdmin, unlockUser);

/**
 * @swagger
 * /admin/employees:
 *   get:
 *     summary: Get all employees
 *     description: Retrieve a list of all employees with their complete information
 *     tags: [Employee Management]
 *     responses:
 *       200:
 *         description: Employees retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/employees', requireAdminOrManager, getAllEmployees);

/**
 * @swagger
 * /admin/employees:
 *   post:
 *     summary: Create a new employee
 *     description: Add a new employee to the system
 *     tags: [Employee Management]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateEmployeeRequest'
 *     responses:
 *       201:
 *         description: Employee created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         employeeId:
 *                           type: integer
 *                           example: 51
 *       400:
 *         description: Bad request - validation errors
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/employees', requireAdminOrManager, createEmployee);

/**
 * @swagger
 * /admin/employees/{id}:
 *   put:
 *     summary: Update an employee
 *     description: Update an existing employee's information
 *     tags: [Employee Management]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *         example: 1
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 example: John
 *               lastName:
 *                 type: string
 *                 example: Doe
 *               position:
 *                 type: string
 *                 example: Senior Barista
 *               salary:
 *                 type: number
 *                 format: float
 *                 example: 1200.00
 *               phoneNumber:
 *                 type: string
 *                 example: "+855-111-222-333"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               address:
 *                 type: string
 *                 example: "456 Updated Street, Phnom Penh"
 *     responses:
 *       200:
 *         description: Employee updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       404:
 *         description: Employee not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/employees/:id', requireAdminOrManager, updateEmployee);

/**
 * @swagger
 * /admin/employees/{id}:
 *   delete:
 *     summary: Delete an employee
 *     description: Remove an employee from the system
 *     tags: [Employee Management]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *         example: 1
 *     responses:
 *       200:
 *         description: Employee deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SuccessResponse'
 *       404:
 *         description: Employee not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.delete('/employees/:id', requireAdmin, deleteEmployee);

/**
 * @swagger
 * /admin/audit-logs:
 *   get:
 *     summary: Get audit logs
 *     description: Retrieve paginated audit logs showing all database operations
 *     tags: [Audit & Security]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of records to return
 *         example: 20
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of records to skip
 *         example: 0
 *     responses:
 *       200:
 *         description: Audit logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AuditLogEntry'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/audit-logs', requireAdmin, getAuditLogs);

/**
 * @swagger
 * /admin/security-report:
 *   get:
 *     summary: Get security report
 *     description: Generate comprehensive security report including user status, risk assessment, and recent audit activity
 *     tags: [Audit & Security]
 *     responses:
 *       200:
 *         description: Security report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SecurityReport'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/security-report', requireAdmin, getSecurityReport);

/**
 * @swagger
 * /admin/activity-logs:
 *   get:
 *     summary: Get activity logs
 *     description: Retrieve paginated activity logs with filtering options
 *     tags: [Activity Monitoring]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of records to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of records to skip
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter logs from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter logs until this date
 *       - in: query
 *         name: ipAddress
 *         schema:
 *           type: string
 *         description: Filter by IP address
 *       - in: query
 *         name: method
 *         schema:
 *           type: string
 *           enum: [GET, POST, PUT, DELETE, PATCH]
 *         description: Filter by HTTP method
 *       - in: query
 *         name: statusCode
 *         schema:
 *           type: integer
 *         description: Filter by HTTP status code
 *     responses:
 *       200:
 *         description: Activity logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ActivityLog'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/activity-logs', requireAdminOrManager, getActivityLogs);

/**
 * @swagger
 * /admin/security-logs:
 *   get:
 *     summary: Get security logs
 *     description: Retrieve security-related logs including failed login attempts and suspicious activities
 *     tags: [Activity Monitoring]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of records to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Security logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/security-logs', requireAdmin, getSecurityLogs);

/**
 * @swagger
 * /admin/api-usage-stats:
 *   get:
 *     summary: Get API usage statistics
 *     description: Retrieve statistics about API usage, performance, and endpoint popularity
 *     tags: [System Monitoring]
 *     responses:
 *       200:
 *         description: API usage statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalRequests:
 *                       type: integer
 *                       example: 10000
 *                     averageResponseTime:
 *                       type: number
 *                       example: 125.5
 *                     popularEndpoints:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           endpoint:
 *                             type: string
 *                           requestCount:
 *                             type: integer
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/api-usage-stats', requireAdminOrManager, getApiUsageStats);

/**
 * @swagger
 * /admin/system-metrics:
 *   get:
 *     summary: Get system metrics
 *     description: Retrieve real-time system health metrics including API performance, security threats, and system status
 *     tags: [System Monitoring]
 *     responses:
 *       200:
 *         description: System metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SystemMetrics'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/system-metrics', requireAdminOrManager, getSystemMetrics);

/**
 * @swagger
 * /admin/clean-logs:
 *   post:
 *     summary: Clean old logs
 *     description: Remove old log entries to maintain system performance and storage
 *     tags: [System Monitoring]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               daysToKeep:
 *                 type: integer
 *                 default: 30
 *                 description: Number of days of logs to keep
 *                 example: 30
 *     responses:
 *       200:
 *         description: Logs cleaned successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         deletedRecords:
 *                           type: integer
 *                           example: 1500
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/clean-logs', requireAdmin, cleanOldLogs);

export default router;
