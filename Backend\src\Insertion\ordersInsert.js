import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertOrders() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Get customer and product IDs for foreign keys
    const [customers] = await connection.query('SELECT customer_id FROM customers');
    const [products] = await connection.query('SELECT product_id, unit_price FROM products');
    if (customers.length === 0 || products.length === 0) {
      throw new Error('Customers or Products table is empty. Please insert data first.');
    }
    const statusOptions = ['Pending', 'Completed'];
    const batchSize = 10000; // Insert in batches to avoid memory/timeout issues
    let totalInserted = 0;
    for (let batch = 0; batch < 100; batch++) {
      const orders = [];
      for (let i = 0; i < batchSize; i++) {
        const customer = customers[Math.floor(Math.random() * customers.length)].customer_id;
        const productObj = products[Math.floor(Math.random() * products.length)];
        const product = productObj.product_id;
        const unitPrice = productObj.unit_price;
        const quantity = faker.number.int({ min: 1, max: 10 });
        const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];
        orders.push([
          customer,
          product,
          quantity,
          unitPrice,
          status
        ]);
      }
      const sql = `INSERT INTO orders (customer_id, product_id, quantity, unit_price, status_order) VALUES ?`;
      await connection.query(sql, [orders]);
      totalInserted += batchSize;
      console.log(`Inserted ${totalInserted} orders so far...`);
    }
    console.log('Inserted 1,000,000 orders successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertOrders();
