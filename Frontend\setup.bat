@echo off
echo 🚀 Setting up Coffee Management System Frontend...

echo 📦 Installing dependencies...
npm install

echo ✨ Installing animation library...
npm install framer-motion@^10.16.16

echo.
echo ✅ Frontend setup complete!
echo.
echo 🎯 Next steps:
echo 1. Run 'npm run dev' to start the development server
echo 2. Open http://localhost:5173 in your browser
echo 3. Use admin/admin123 to login
echo.
echo 🌟 Features included:
echo   - Beautiful Starbucks-inspired design
echo   - Smooth animations with Framer Motion
echo   - Authentication system
echo   - Responsive admin dashboard
echo   - Activity logging
echo.
pause
