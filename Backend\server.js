import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { connectToAivenDB } from './src/config/ConnectSQlWithAiven.js'; 

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware - Order is important!
// // Trust proxy for accurate IP addresses (if behind reverse proxy)
// app.set('trust proxy', true);

// Rate limiting (apply early)
// app.use(rateLimitLogger(15 * 60 * 1000, 100)); // 100 requests per 15 minutes


// Standard middleware
app.use(morgan('dev'));
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors());
// app.use(express.json({ limit: '10mb' }));
// app.use(express.urlencoded({ extended: true }));



// Test Aiven database connection on startup
async function testAivenConnection() {
    try {
        const connection = await connectToAivenDB();
        console.log('✅ Connected to Aiven MySQL database successfully!');
        
        // Test query
        const [rows] = await connection.execute('SELECT 1 + 1 AS solution');
        console.log('🔍 Test query result:', rows);
        
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Aiven database connection failed:', error.message);
        return false;
    }
}

testAivenConnection();

// Routes
app.get('/', (req, res) => {
    res.json({
        message: 'Coffee Management System API',
        version: '1.0.0',
        status: 'running',
        documentation: `http://localhost:${PORT}/api-docs`
    });
});

// Swagger API Documentation
// app.use('/api-docs', serveSwagger, setupSwagger);

// // Admin routes (protected)
// console.log('About to register admin routes...');
// try {
//     app.use('/api/admin', adminRoutes);
//     console.log('✅ Admin routes registered successfully');
// } catch (error) {
//     console.error('❌ Error registering admin routes:', error);
//     process.exit(1);
// }

// // Global error handler
// app.use((err, req, res, next) => {
//     console.error('Global error:', err);
//     res.status(500).json({
//         success: false,
//         message: 'Internal server error',
//         error: process.env.NODE_ENV === 'development' ? err.message : undefined
//     });
// });

// // 404 handler
// app.use((req, res) => {
//     res.status(404).json({
//         success: false,
//         message: 'Route not found'
//     });
// });

app.listen(PORT, () => {
    console.log(`🚀 Server is running on http://localhost:${PORT}`);
    console.log(`📊 Admin Panel: http://localhost:${PORT}/api/admin`);
    console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
});
