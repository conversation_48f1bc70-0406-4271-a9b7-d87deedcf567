import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { connectToAivenDB } from './src/config/ConnectSQlWithAiven.js';

// Import routes
import customerRoutes from './src/routes/customerRoutes.js';
import employeeRoutes from './src/routes/employeeRoutes.js';
import supplierRoutes from './src/routes/supplierRoutes.js';
import materialRoutes from './src/routes/materialRoutes.js';
import productRoutes from './src/routes/productRoutes.js';
import orderRoutes from './src/routes/orderRoutes.js';
import paymentRoutes from './src/routes/paymentRoutes.js';
import analyticsRoutes from './src/routes/analyticsRoutes.js';
import authRoutes from './src/routes/authRoutes.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware - Order is important!
app.use(morgan('dev'));
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));



// Test Aiven database connection on startup
async function testAivenConnection() {
    try {
        const connection = await connectToAivenDB();
        console.log('✅ Connected to Aiven MySQL database successfully!');
        
        // Test query
        const [rows] = await connection.execute('SELECT 1 + 1 AS solution');
        console.log('🔍 Test query result:', rows);
        
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Aiven database connection failed:', error.message);
        return false;
    }
}

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/materials', materialRoutes);
app.use('/api/products', productRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/analytics', analyticsRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        message: 'Coffee Management System API is running',
        timestamp: new Date().toISOString()
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Welcome to Coffee Management System API',
        version: '1.0.0',
        endpoints: {
            auth: '/api/auth',
            customers: '/api/customers',
            employees: '/api/employees',
            suppliers: '/api/suppliers',
            materials: '/api/materials',
            products: '/api/products',
            orders: '/api/orders',
            payments: '/api/payments',
            analytics: '/api/analytics',
            health: '/health'
        }
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error:', err);

    // Database connection errors
    if (err.code === 'ECONNREFUSED' || err.code === 'ER_ACCESS_DENIED_ERROR') {
        return res.status(503).json({
            error: 'Database connection failed',
            message: 'Unable to connect to the database'
        });
    }

    // Validation errors
    if (err.name === 'ValidationError') {
        return res.status(400).json({
            error: 'Validation Error',
            message: err.message
        });
    }

    // Default error
    res.status(err.status || 500).json({
        error: err.message || 'Internal Server Error',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`
    });
});

// Start server
app.listen(PORT, async () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   GET  /                    - API information`);
    console.log(`   GET  /health              - Health check`);
    console.log(`   *    /api/auth            - Authentication and user management`);
    console.log(`   *    /api/customers       - Customer management`);
    console.log(`   *    /api/employees       - Employee management`);
    console.log(`   *    /api/suppliers       - Supplier management`);
    console.log(`   *    /api/materials       - Material management`);
    console.log(`   *    /api/products        - Product management`);
    console.log(`   *    /api/orders          - Order management`);
    console.log(`   *    /api/payments        - Payment management`);
    console.log(`   *    /api/analytics       - Analytics and reports`);

    // Test database connection
    const dbConnected = await testAivenConnection();
    if (!dbConnected) {
        console.log('⚠️  Server started but database connection failed');
        console.log('   Please check your .env file and database configuration');
    }
});
