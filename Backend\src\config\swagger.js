import swaggerJSDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Coffee Management System API',
            version: '1.0.0',
            description: 'A comprehensive REST API for managing coffee shop operations including admin dashboard, user management, employee management, orders, products, suppliers, and security monitoring.',
            contact: {
                name: 'Coffee Management System Support',
                email: '<EMAIL>'
            },
            license: {
                name: 'MIT',
                url: 'https://opensource.org/licenses/MIT'
            }
        },
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                    description: 'JWT Authorization header using the Bearer scheme. Example: "Bearer {token}"'
                }
            },
            schemas: {
                // Dashboard & Statistics
                DashboardStats: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: true },
                        data: {
                            type: 'object',
                            properties: {
                                counts: {
                                    type: 'object',
                                    properties: {
                                        customers: { type: 'integer', example: 1000 },
                                        employees: { type: 'integer', example: 50 },
                                        orders: { type: 'integer', example: 25000 },
                                        products: { type: 'integer', example: 200 },
                                        suppliers: { type: 'integer', example: 50 },
                                        payments: { type: 'integer', example: 20000 }
                                    }
                                },
                                recentActivities: {
                                    type: 'array',
                                    items: { $ref: '#/components/schemas/AuditLogEntry' }
                                },
                                revenueData: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            date: { type: 'string', format: 'date' },
                                            revenue: { type: 'number', format: 'float' }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                
                // User Management
                User: {
                    type: 'object',
                    properties: {
                        user_name: { type: 'string', example: 'admin123' },
                        role_type: { type: 'string', enum: ['admin', 'manager', 'cashier', 'barista', 'vendor', 'customer'], example: 'admin' },
                        is_active: { type: 'boolean', example: true },
                        last_login: { type: 'string', format: 'date-time', nullable: true },
                        login_attempts: { type: 'integer', example: 0 },
                        locked_until: { type: 'string', format: 'date-time', nullable: true },
                        account_status: { type: 'string', enum: ['ACTIVE', 'INACTIVE', 'LOCKED'], example: 'ACTIVE' },
                        employee_name: { type: 'string', example: 'John Doe', nullable: true },
                        employee_position: { type: 'string', example: 'Manager', nullable: true }
                    }
                },
                
                CreateUserRequest: {
                    type: 'object',
                    required: ['username', 'password', 'roleType', 'employeeId'],
                    properties: {
                        username: { type: 'string', example: 'newuser123' },
                        password: { type: 'string', example: 'SecurePass123!' },
                        roleType: { type: 'string', enum: ['admin', 'manager', 'cashier', 'barista', 'vendor', 'customer'], example: 'cashier' },
                        employeeId: { type: 'integer', example: 5 }
                    }
                },
                
                // Employee Management
                Employee: {
                    type: 'object',
                    properties: {
                        employee_id: { type: 'integer', example: 1 },
                        first_name: { type: 'string', example: 'John' },
                        last_name: { type: 'string', example: 'Doe' },
                        position: { type: 'string', example: 'Manager' },
                        hire_date: { type: 'string', format: 'date', example: '2023-01-15' },
                        salary: { type: 'number', format: 'float', example: 1500.00 },
                        phone_number: { type: 'string', example: '+855-123-456-789' },
                        email: { type: 'string', format: 'email', example: '<EMAIL>' },
                        address: { type: 'string', example: '123 Main Street, Phnom Penh' }
                    }
                },
                
                CreateEmployeeRequest: {
                    type: 'object',
                    required: ['firstName', 'lastName', 'position', 'hireDate', 'salary', 'phoneNumber', 'email'],
                    properties: {
                        firstName: { type: 'string', example: 'Jane' },
                        lastName: { type: 'string', example: 'Smith' },
                        position: { type: 'string', example: 'Barista' },
                        hireDate: { type: 'string', format: 'date', example: '2024-01-01' },
                        salary: { type: 'number', format: 'float', example: 800.00 },
                        phoneNumber: { type: 'string', example: '+855-987-654-321' },
                        email: { type: 'string', format: 'email', example: '<EMAIL>' },
                        address: { type: 'string', example: '456 Coffee Street, Siem Reap' }
                    }
                },
                
                // Customer Management
                Customer: {
                    type: 'object',
                    properties: {
                        customer_id: { type: 'integer', example: 1 },
                        first_name: { type: 'string', example: 'Alice' },
                        last_name: { type: 'string', example: 'Johnson' },
                        email: { type: 'string', format: 'email', example: '<EMAIL>' },
                        phone_number: { type: 'string', example: '+855-111-222-333' }
                    }
                },
                
                // Product Management
                Product: {
                    type: 'object',
                    properties: {
                        product_id: { type: 'integer', example: 1 },
                        product_name: { type: 'string', example: 'Cappuccino' },
                        material_eating_id: { type: 'integer', example: 15 },
                        material_object_id: { type: 'integer', example: 8 },
                        category_name: { type: 'string', example: 'Coffee' },
                        description: { type: 'string', example: 'Rich and creamy cappuccino with steamed milk' },
                        unit_price: { type: 'number', format: 'float', example: 4.50 }
                    }
                },
                
                // Order Management
                Order: {
                    type: 'object',
                    properties: {
                        order_id: { type: 'integer', example: 1001 },
                        customer_id: { type: 'integer', example: 25 },
                        product_id: { type: 'integer', example: 5 },
                        quantity: { type: 'integer', example: 2 },
                        unit_price: { type: 'number', format: 'float', example: 4.50 },
                        status_order: { type: 'string', enum: ['Pending', 'Completed'], example: 'Pending' },
                        order_date: { type: 'string', format: 'date-time' }
                    }
                },
                
                // Payment Management
                Payment: {
                    type: 'object',
                    properties: {
                        payment_id: { type: 'integer', example: 1 },
                        employee_id: { type: 'integer', example: 3 },
                        order_id: { type: 'integer', example: 1001 },
                        payment_type: { type: 'string', enum: ['ABA', 'Aceleda', 'Cash', 'Other'], example: 'Cash' },
                        amount_money: { type: 'number', format: 'float', example: 9.00 },
                        payment_date: { type: 'string', format: 'date-time' }
                    }
                },
                
                // Supplier Management
                Supplier: {
                    type: 'object',
                    properties: {
                        supplier_id: { type: 'integer', example: 1 },
                        supplies_name: { type: 'string', example: 'Coffee Bean Co.' },
                        contact_email: { type: 'string', format: 'email', example: '<EMAIL>' },
                        phone_number: { type: 'integer', example: 123456789 },
                        location_url: { type: 'string', example: '789 Supply Street, Phnom Penh' },
                        category: { type: 'string', enum: ['eating material', 'material object'], example: 'eating material' }
                    }
                },
                
                // Material Management
                MaterialEating: {
                    type: 'object',
                    properties: {
                        material_eating_id: { type: 'integer', example: 1 },
                        supplier_id: { type: 'integer', example: 5 },
                        name_material: { type: 'string', example: 'Arabica Coffee Beans' },
                        quantity: { type: 'integer', example: 50 },
                        unit_price: { type: 'number', format: 'float', example: 25.00 },
                        category: { type: 'string', example: 'coffee bean' },
                        employee_id: { type: 'integer', example: 2 }
                    }
                },
                
                MaterialObject: {
                    type: 'object',
                    properties: {
                        material_object_id: { type: 'integer', example: 1 },
                        supplier_id: { type: 'integer', example: 3 },
                        unit_price: { type: 'number', format: 'float', example: 0.25 },
                        name_material: { type: 'string', example: 'Paper Cups' },
                        category: { type: 'string', example: 'paper cup' },
                        quantity: { type: 'integer', example: 1000 },
                        employee_id: { type: 'integer', example: 4 }
                    }
                },
                
                // Loyalty Points
                LoyaltyPoints: {
                    type: 'object',
                    properties: {
                        loyalty_id: { type: 'integer', example: 1 },
                        order_id: { type: 'integer', example: 1001 },
                        points_earned: { type: 'integer', example: 50 }
                    }
                },
                
                // Audit & Security
                AuditLogEntry: {
                    type: 'object',
                    properties: {
                        log_id: { type: 'integer', example: 1 },
                        table_name: { type: 'string', example: 'employees' },
                        operation_type: { type: 'string', enum: ['INSERT', 'UPDATE', 'DELETE'], example: 'INSERT' },
                        user_name: { type: 'string', example: 'admin123' },
                        operation_time: { type: 'string', format: 'date-time' },
                        old_values: { type: 'string', nullable: true },
                        new_values: { type: 'string', nullable: true }
                    }
                },
                
                SecurityReport: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: true },
                        data: {
                            type: 'object',
                            properties: {
                                userStatusSummary: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            account_status: { type: 'string' },
                                            user_count: { type: 'integer' }
                                        }
                                    }
                                },
                                recentAuditActivity: { type: 'array', items: { $ref: '#/components/schemas/AuditLogEntry' } },
                                highRiskUsers: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            user_name: { type: 'string' },
                                            role_type: { type: 'string' },
                                            login_attempts: { type: 'integer' },
                                            risk_level: { type: 'string' },
                                            locked_until: { type: 'string', format: 'date-time', nullable: true }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                
                ActivityLog: {
                    type: 'object',
                    properties: {
                        log_id: { type: 'integer', example: 1 },
                        timestamp: { type: 'string', format: 'date-time' },
                        ip_address: { type: 'string', example: '***********' },
                        user_agent: { type: 'string', example: 'Mozilla/5.0...' },
                        method: { type: 'string', example: 'GET' },
                        url: { type: 'string', example: '/api/admin/dashboard/stats' },
                        status_code: { type: 'integer', example: 200 },
                        response_time_ms: { type: 'integer', example: 150 },
                        user_id: { type: 'string', nullable: true }
                    }
                },
                
                SystemMetrics: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: true },
                        data: {
                            type: 'object',
                            properties: {
                                apiUsage: {
                                    type: 'object',
                                    properties: {
                                        requests_last_hour: { type: 'integer' },
                                        avg_response_time: { type: 'number' },
                                        unique_ips: { type: 'integer' }
                                    }
                                },
                                securityThreats: {
                                    type: 'object',
                                    properties: {
                                        threats_last_24h: { type: 'integer' },
                                        threat_sources: { type: 'integer' }
                                    }
                                },
                                systemHealth: {
                                    type: 'object',
                                    properties: {
                                        server_errors: { type: 'integer' },
                                        client_errors: { type: 'integer' },
                                        successful_requests: { type: 'integer' }
                                    }
                                }
                            }
                        }
                    }
                },
                
                // Standard Response Types
                SuccessResponse: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: true },
                        message: { type: 'string', example: 'Operation completed successfully' },
                        data: { type: 'object' }
                    }
                },
                
                ErrorResponse: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: false },
                        message: { type: 'string', example: 'Error message' },
                        error: { type: 'string', example: 'Detailed error information' }
                    }
                },
                
                PaginatedResponse: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean', example: true },
                        data: { type: 'array', items: {} },
                        total: { type: 'integer', example: 100 },
                        limit: { type: 'integer', example: 50 },
                        offset: { type: 'integer', example: 0 }
                    }
                }
            }
        },
        servers: [
            {
                url: `http://localhost:${process.env.PORT || 3000}/api`,
                description: 'Development server'
            }
        ]
    },
    apis: ['./src/routes/*.js', './src/controllers/*.js', './src/models/*.js'],
};

const swaggerSpec = swaggerJSDoc(options);

export const serveSwagger = swaggerUi.serve;
export const setupSwagger = swaggerUi.setup(swaggerSpec);
