import DatabaseService from '../services/databaseService.js';

/**
 * Customer Controller
 * Handles all customer-related operations
 */

class CustomerController {
    /**
     * Get all customers with pagination
     */
    static async getAllCustomers(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search || '';

            let baseQuery = `
                SELECT 
                    customer_id,
                    first_name,
                    last_name,
                    email,
                    phone_number,
                    registration_date
                FROM customers
            `;
            
            let params = [];
            
            if (search) {
                baseQuery += ` WHERE 
                    first_name LIKE ? OR 
                    last_name LIKE ? OR 
                    email LIKE ?
                `;
                const searchParam = `%${search}%`;
                params = [searchParam, searchParam, searchParam];
            }
            
            baseQuery += ` ORDER BY registration_date DESC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Customers retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting customers:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve customers',
                error: error.message
            });
        }
    }

    /**
     * Get customer by ID
     */
    static async getCustomerById(req, res) {
        try {
            const { id } = req.params;
            
            const customer = await DatabaseService.executeQuerySingle(
                `SELECT 
                    customer_id,
                    first_name,
                    last_name,
                    email,
                    phone_number,
                    registration_date
                FROM customers 
                WHERE customer_id = ?`,
                [id]
            );

            if (!customer) {
                return res.status(404).json({
                    success: false,
                    message: 'Customer not found'
                });
            }

            res.json({
                success: true,
                message: 'Customer retrieved successfully',
                data: customer
            });
        } catch (error) {
            console.error('Error getting customer:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve customer',
                error: error.message
            });
        }
    }

    /**
     * Create new customer
     */
    static async createCustomer(req, res) {
        try {
            const { first_name, last_name, email, phone_number } = req.body;

            // Validation
            if (!first_name || !last_name || !email || !phone_number) {
                return res.status(400).json({
                    success: false,
                    message: 'All fields are required: first_name, last_name, email, phone_number'
                });
            }

            // Check if email already exists
            const existingCustomer = await DatabaseService.recordExists('customers', 'email', email);
            if (existingCustomer) {
                return res.status(409).json({
                    success: false,
                    message: 'Customer with this email already exists'
                });
            }

            const customerId = await DatabaseService.executeInsert(
                `INSERT INTO customers (first_name, last_name, email, phone_number) 
                 VALUES (?, ?, ?, ?)`,
                [first_name, last_name, email, phone_number]
            );

            const newCustomer = await DatabaseService.executeQuerySingle(
                `SELECT 
                    customer_id,
                    first_name,
                    last_name,
                    email,
                    phone_number,
                    registration_date
                FROM customers 
                WHERE customer_id = ?`,
                [customerId]
            );

            res.status(201).json({
                success: true,
                message: 'Customer created successfully',
                data: newCustomer
            });
        } catch (error) {
            console.error('Error creating customer:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create customer',
                error: error.message
            });
        }
    }

    /**
     * Update customer
     */
    static async updateCustomer(req, res) {
        try {
            const { id } = req.params;
            const { first_name, last_name, email, phone_number } = req.body;

            // Check if customer exists
            const existingCustomer = await DatabaseService.recordExists('customers', 'customer_id', id);
            if (!existingCustomer) {
                return res.status(404).json({
                    success: false,
                    message: 'Customer not found'
                });
            }

            // Check if email is being changed and if it already exists
            if (email) {
                const emailExists = await DatabaseService.executeQuerySingle(
                    'SELECT customer_id FROM customers WHERE email = ? AND customer_id != ?',
                    [email, id]
                );
                if (emailExists) {
                    return res.status(409).json({
                        success: false,
                        message: 'Email already exists for another customer'
                    });
                }
            }

            const affectedRows = await DatabaseService.executeUpdate(
                `UPDATE customers 
                 SET first_name = ?, last_name = ?, email = ?, phone_number = ?
                 WHERE customer_id = ?`,
                [first_name, last_name, email, phone_number, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Customer not found or no changes made'
                });
            }

            const updatedCustomer = await DatabaseService.executeQuerySingle(
                `SELECT 
                    customer_id,
                    first_name,
                    last_name,
                    email,
                    phone_number,
                    registration_date
                FROM customers 
                WHERE customer_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Customer updated successfully',
                data: updatedCustomer
            });
        } catch (error) {
            console.error('Error updating customer:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update customer',
                error: error.message
            });
        }
    }

    /**
     * Delete customer
     */
    static async deleteCustomer(req, res) {
        try {
            const { id } = req.params;

            // Check if customer has orders
            const hasOrders = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM orders WHERE customer_id = ? LIMIT 1',
                [id]
            );

            if (hasOrders) {
                return res.status(409).json({
                    success: false,
                    message: 'Cannot delete customer with existing orders'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                'DELETE FROM customers WHERE customer_id = ?',
                [id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Customer not found'
                });
            }

            res.json({
                success: true,
                message: 'Customer deleted successfully'
            });
        } catch (error) {
            console.error('Error deleting customer:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete customer',
                error: error.message
            });
        }
    }

    /**
     * Get customer order history
     */
    static async getCustomerOrders(req, res) {
        try {
            const { id } = req.params;
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;

            // Check if customer exists
            const customerExists = await DatabaseService.recordExists('customers', 'customer_id', id);
            if (!customerExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Customer not found'
                });
            }

            const baseQuery = `
                SELECT 
                    o.order_id,
                    p.product_name,
                    o.quantity,
                    o.unit_price,
                    o.order_date,
                    o.status_order,
                    (o.quantity * o.unit_price) as total_amount
                FROM orders o
                JOIN products p ON o.product_id = p.product_id
                WHERE o.customer_id = ?
                ORDER BY o.order_date DESC
            `;

            const result = await DatabaseService.getPaginatedResults(baseQuery, [id], page, limit);

            res.json({
                success: true,
                message: 'Customer orders retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting customer orders:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve customer orders',
                error: error.message
            });
        }
    }
}

export default CustomerController;
