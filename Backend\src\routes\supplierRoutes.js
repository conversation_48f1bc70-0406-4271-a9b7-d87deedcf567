import express from 'express';
import SupplierController from '../controllers/supplierController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Supplier:
 *       type: object
 *       required:
 *         - supplies_name
 *         - contact_email
 *         - phone_number
 *         - location_url
 *       properties:
 *         supplier_id:
 *           type: integer
 *           description: Auto-generated supplier ID
 *         supplies_name:
 *           type: string
 *           maxLength: 255
 *           description: Supplier's name
 *         contact_email:
 *           type: string
 *           maxLength: 255
 *           format: email
 *           description: Supplier's contact email
 *         phone_number:
 *           type: integer
 *           description: Supplier's phone number
 *         location_url:
 *           type: string
 *           maxLength: 255
 *           description: Supplier's location URL
 *         category:
 *           type: string
 *           enum: ['eating material', 'material object']
 *           default: 'material object'
 *           description: Supplier category
 *       example:
 *         supplier_id: 1
 *         supplies_name: "Coffee Bean Suppliers Inc"
 *         contact_email: "<EMAIL>"
 *         phone_number: 1234567890
 *         location_url: "https://maps.google.com/location"
 *         category: "eating material"
 *
 *     SupplierInput:
 *       type: object
 *       required:
 *         - supplies_name
 *         - contact_email
 *         - phone_number
 *         - location_url
 *       properties:
 *         supplies_name:
 *           type: string
 *           maxLength: 255
 *         contact_email:
 *           type: string
 *           maxLength: 255
 *           format: email
 *         phone_number:
 *           type: integer
 *         location_url:
 *           type: string
 *           maxLength: 255
 *         category:
 *           type: string
 *           enum: ['eating material', 'material object']
 *           default: 'material object'
 *       example:
 *         supplies_name: "Coffee Bean Suppliers Inc"
 *         contact_email: "<EMAIL>"
 *         phone_number: 1234567890
 *         location_url: "https://maps.google.com/location"
 *         category: "eating material"
 */

/**
 * @swagger
 * /api/suppliers:
 *   get:
 *     summary: Get all suppliers with pagination and filtering
 *     tags: [Suppliers]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of suppliers per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or email
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: ['eating material', 'material object']
 *         description: Filter by category
 *     responses:
 *       200:
 *         description: Suppliers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Supplier'
 *                 pagination:
 *                   type: object
 */
router.get('/', SupplierController.getAllSuppliers);

/**
 * @swagger
 * /api/suppliers/{id}:
 *   get:
 *     summary: Get supplier by ID
 *     tags: [Suppliers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Supplier ID
 *     responses:
 *       200:
 *         description: Supplier retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Supplier'
 *       404:
 *         description: Supplier not found
 */
router.get('/:id', SupplierController.getSupplierById);

/**
 * @swagger
 * /api/suppliers:
 *   post:
 *     summary: Create a new supplier
 *     tags: [Suppliers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SupplierInput'
 *     responses:
 *       201:
 *         description: Supplier created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Supplier'
 *       400:
 *         description: Validation error
 */
router.post('/', SupplierController.createSupplier);

/**
 * @swagger
 * /api/suppliers/{id}:
 *   put:
 *     summary: Update supplier
 *     tags: [Suppliers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Supplier ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SupplierInput'
 *     responses:
 *       200:
 *         description: Supplier updated successfully
 *       404:
 *         description: Supplier not found
 */
router.put('/:id', SupplierController.updateSupplier);

/**
 * @swagger
 * /api/suppliers/{id}:
 *   delete:
 *     summary: Delete supplier
 *     tags: [Suppliers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Supplier ID
 *     responses:
 *       200:
 *         description: Supplier deleted successfully
 *       404:
 *         description: Supplier not found
 *       409:
 *         description: Cannot delete supplier with existing material records
 */
router.delete('/:id', SupplierController.deleteSupplier);

/**
 * @swagger
 * /api/suppliers/category/{category}:
 *   get:
 *     summary: Get suppliers by category
 *     tags: [Suppliers]
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: ['eating material', 'material object']
 *         description: Supplier category
 *     responses:
 *       200:
 *         description: Suppliers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Supplier'
 *       400:
 *         description: Invalid category
 */
router.get('/category/:category', SupplierController.getSuppliersByCategory);

/**
 * @swagger
 * /api/suppliers/{id}/spending:
 *   get:
 *     summary: Get supplier spending summary
 *     tags: [Suppliers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Supplier ID
 *     responses:
 *       200:
 *         description: Supplier spending retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     supplies_name:
 *                       type: string
 *                     category:
 *                       type: string
 *                     total_spent:
 *                       type: number
 *       404:
 *         description: Supplier not found
 */
router.get('/:id/spending', SupplierController.getSupplierSpending);

export default router;
