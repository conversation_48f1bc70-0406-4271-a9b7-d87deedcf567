import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
dotenv.config();

export async function connectToAivenDB() {
  const connection = await mysql.createConnection({
    host: process.env.DBP_HOST,
    port: process.env.DBP_PORT,
    user: process.env.DBP_USER,
    password: process.env.DBP_PASSWORD,
    database: process.env.DBP_NAME,
  });
  console.log('Connected to MySQL database!');
  // Example query
  const [rows] = await connection.execute('SELECT 1 + 1 AS solution');
  console.log('Test query result:', rows);
  return connection; // Return the connection object for use elsewhere
}

// await connectToAivenDB();


