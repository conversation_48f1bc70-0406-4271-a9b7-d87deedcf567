import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Coffee Management System API',
      version: '1.0.0',
      description: 'A comprehensive API for managing coffee shop operations.',
    },
    servers: [
      {
        url: `http://localhost:${PORT}`,
        description: 'Development server',
      },
    ],
  },
  apis: ['./src/routes/*.js'], // Path to the API docs
};

const specs = swaggerJsdoc(swaggerOptions);

// Middleware
app.use(morgan('dev'));
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Swagger UI
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));

// Test routes one by one
try {
    console.log('Testing customer routes...');
    const customerRoutes = await import('./src/routes/customerRoutes.js');
    app.use('/api/customers', customerRoutes.default);
    console.log('✅ Customer routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading customer routes:', error.message);
}

try {
    console.log('Testing employee routes...');
    const employeeRoutes = await import('./src/routes/employeeRoutes.js');
    app.use('/api/employees', employeeRoutes.default);
    console.log('✅ Employee routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading employee routes:', error.message);
}

try {
    console.log('Testing supplier routes...');
    const supplierRoutes = await import('./src/routes/supplierRoutes.js');
    app.use('/api/suppliers', supplierRoutes.default);
    console.log('✅ Supplier routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading supplier routes:', error.message);
}

try {
    console.log('Testing material routes...');
    const materialRoutes = await import('./src/routes/materialRoutes.js');
    app.use('/api/materials', materialRoutes.default);
    console.log('✅ Material routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading material routes:', error.message);
}

try {
    console.log('Testing product routes...');
    const productRoutes = await import('./src/routes/productRoutes.js');
    app.use('/api/products', productRoutes.default);
    console.log('✅ Product routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading product routes:', error.message);
}

try {
    console.log('Testing order routes...');
    const orderRoutes = await import('./src/routes/orderRoutes.js');
    app.use('/api/orders', orderRoutes.default);
    console.log('✅ Order routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading order routes:', error.message);
}

try {
    console.log('Testing payment routes...');
    const paymentRoutes = await import('./src/routes/paymentRoutes.js');
    app.use('/api/payments', paymentRoutes.default);
    console.log('✅ Payment routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading payment routes:', error.message);
}

try {
    console.log('Testing analytics routes...');
    const analyticsRoutes = await import('./src/routes/analyticsRoutes.js');
    app.use('/api/analytics', analyticsRoutes.default);
    console.log('✅ Analytics routes loaded successfully');
} catch (error) {
    console.error('❌ Error loading analytics routes:', error.message);
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ 
        status: 'OK', 
        message: 'Coffee Management System API is running',
        timestamp: new Date().toISOString()
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Welcome to Coffee Management System API',
        documentation: '/api-docs',
        health: '/health'
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(err.status || 500).json({
        error: err.message || 'Internal Server Error',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📚 API Documentation available at http://localhost:${PORT}/api-docs`);
});
