import DatabaseService from '../services/databaseService.js';

/**
 * Payment Controller
 * Handles all payment-related operations
 * Note: Creating a payment automatically triggers loyalty points via database trigger
 */

class PaymentController {
    /**
     * Get all payments with pagination and filtering
     */
    static async getAllPayments(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const employee_id = req.query.employee_id || '';
            const payment_type = req.query.payment_type || '';
            const date_from = req.query.date_from || '';
            const date_to = req.query.date_to || '';

            let baseQuery = `
                SELECT 
                    p.payment_id,
                    p.employee_id,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name,
                    p.order_id,
                    p.payment_date,
                    p.payment_type,
                    p.amount_money,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    pr.product_name
                FROM payments p
                JOIN employees e ON p.employee_id = e.employee_id
                JOIN orders o ON p.order_id = o.order_id
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products pr ON o.product_id = pr.product_id
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (employee_id) {
                whereConditions.push('p.employee_id = ?');
                params.push(employee_id);
            }
            
            if (payment_type) {
                whereConditions.push('p.payment_type = ?');
                params.push(payment_type);
            }
            
            if (date_from) {
                whereConditions.push('DATE(p.payment_date) >= ?');
                params.push(date_from);
            }
            
            if (date_to) {
                whereConditions.push('DATE(p.payment_date) <= ?');
                params.push(date_to);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY p.payment_date DESC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Payments retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting payments:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve payments',
                error: error.message
            });
        }
    }

    /**
     * Get payment by ID
     */
    static async getPaymentById(req, res) {
        try {
            const { id } = req.params;
            
            const payment = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.payment_id,
                    p.employee_id,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name,
                    p.order_id,
                    p.payment_date,
                    p.payment_type,
                    p.amount_money,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    pr.product_name,
                    o.quantity,
                    o.unit_price
                FROM payments p
                JOIN employees e ON p.employee_id = e.employee_id
                JOIN orders o ON p.order_id = o.order_id
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products pr ON o.product_id = pr.product_id
                WHERE p.payment_id = ?`,
                [id]
            );

            if (!payment) {
                return res.status(404).json({
                    success: false,
                    message: 'Payment not found'
                });
            }

            res.json({
                success: true,
                message: 'Payment retrieved successfully',
                data: payment
            });
        } catch (error) {
            console.error('Error getting payment:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve payment',
                error: error.message
            });
        }
    }

    /**
     * Create new payment
     * This will automatically trigger loyalty points creation via database trigger
     */
    static async createPayment(req, res) {
        try {
            const { 
                employee_id, 
                order_id, 
                payment_type, 
                amount_money 
            } = req.body;

            // Validation
            if (!employee_id || !order_id || !amount_money) {
                return res.status(400).json({
                    success: false,
                    message: 'Required fields: employee_id, order_id, amount_money'
                });
            }

            // Validate amount_money is positive
            if (amount_money <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Amount must be greater than 0'
                });
            }

            // Validate payment_type enum
            const validPaymentTypes = ['ABA', 'Aceleda', 'Cash', 'Other'];
            const paymentType = payment_type || 'Other';
            
            if (!validPaymentTypes.includes(paymentType)) {
                return res.status(400).json({
                    success: false,
                    message: `Payment type must be one of: ${validPaymentTypes.join(', ')}`
                });
            }

            // Check if employee exists
            const employeeExists = await DatabaseService.recordExists('employees', 'employee_id', employee_id);
            if (!employeeExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found'
                });
            }

            // Check if order exists and doesn't already have a payment
            const orderCheck = await DatabaseService.executeQuerySingle(
                `SELECT o.order_id, p.payment_id 
                 FROM orders o 
                 LEFT JOIN payments p ON o.order_id = p.order_id 
                 WHERE o.order_id = ?`,
                [order_id]
            );

            if (!orderCheck) {
                return res.status(404).json({
                    success: false,
                    message: 'Order not found'
                });
            }

            if (orderCheck.payment_id) {
                return res.status(409).json({
                    success: false,
                    message: 'Order already has a payment'
                });
            }

            const paymentId = await DatabaseService.executeInsert(
                `INSERT INTO payments (employee_id, order_id, payment_type, amount_money) 
                 VALUES (?, ?, ?, ?)`,
                [employee_id, order_id, paymentType, amount_money]
            );

            // Update order status to completed
            await DatabaseService.executeUpdate(
                'UPDATE orders SET status_order = ? WHERE order_id = ?',
                ['Completed', order_id]
            );

            const newPayment = await DatabaseService.executeQuerySingle(
                `SELECT 
                    p.payment_id,
                    p.employee_id,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name,
                    p.order_id,
                    p.payment_date,
                    p.payment_type,
                    p.amount_money,
                    CONCAT(c.first_name, ' ', c.last_name) as customer_name,
                    pr.product_name
                FROM payments p
                JOIN employees e ON p.employee_id = e.employee_id
                JOIN orders o ON p.order_id = o.order_id
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                JOIN products pr ON o.product_id = pr.product_id
                WHERE p.payment_id = ?`,
                [paymentId]
            );

            res.status(201).json({
                success: true,
                message: 'Payment created successfully (loyalty points automatically added)',
                data: newPayment
            });
        } catch (error) {
            console.error('Error creating payment:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create payment',
                error: error.message
            });
        }
    }

    /**
     * Get payment summary by date range
     */
    static async getPaymentSummary(req, res) {
        try {
            const { date_from, date_to } = req.query;
            
            let query = `
                SELECT 
                    DATE(payment_date) as payment_date,
                    payment_type,
                    COUNT(*) as transaction_count,
                    SUM(amount_money) as total_amount
                FROM payments
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (date_from) {
                whereConditions.push('DATE(payment_date) >= ?');
                params.push(date_from);
            }
            
            if (date_to) {
                whereConditions.push('DATE(payment_date) <= ?');
                params.push(date_to);
            }
            
            if (whereConditions.length > 0) {
                query += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            query += ` GROUP BY DATE(payment_date), payment_type ORDER BY payment_date DESC, payment_type`;

            const summary = await DatabaseService.executeQuery(query, params);

            res.json({
                success: true,
                message: 'Payment summary retrieved successfully',
                data: summary
            });
        } catch (error) {
            console.error('Error getting payment summary:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve payment summary',
                error: error.message
            });
        }
    }

    /**
     * Get daily revenue
     */
    static async getDailyRevenue(req, res) {
        try {
            const { days = 7 } = req.query;
            
            const revenue = await DatabaseService.executeQuery(
                `SELECT 
                    DATE(payment_date) as date,
                    SUM(amount_money) as total_revenue,
                    COUNT(*) as transaction_count
                FROM payments
                WHERE payment_date >= NOW() - INTERVAL ? DAY
                GROUP BY DATE(payment_date)
                ORDER BY date DESC`,
                [days]
            );

            res.json({
                success: true,
                message: `Daily revenue for last ${days} days retrieved successfully`,
                data: revenue
            });
        } catch (error) {
            console.error('Error getting daily revenue:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve daily revenue',
                error: error.message
            });
        }
    }
}

export default PaymentController;
