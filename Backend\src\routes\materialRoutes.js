import express from 'express';
import MaterialController from '../controllers/materialController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     MaterialEating:
 *       type: object
 *       required:
 *         - supplier_id
 *         - name_material
 *         - quantity
 *         - unit_price
 *         - category
 *         - employee_id
 *       properties:
 *         material_eating_id:
 *           type: integer
 *           description: Auto-generated material eating ID
 *         supplier_id:
 *           type: integer
 *           description: Reference to supplier
 *         name_material:
 *           type: string
 *           maxLength: 250
 *           description: Material name
 *         quantity:
 *           type: integer
 *           minimum: 1
 *           description: Material quantity
 *         unit_price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *           description: Unit price
 *         import_date:
 *           type: string
 *           format: date-time
 *           description: Import timestamp
 *         category:
 *           type: string
 *           enum: ['coffee bean', 'matcha', 'sugar', 'condensed milk', 'fresh milk', 'espresso', 'chocolate', 'green tea', 'black tea', 'caramel syrup', 'vanilla syrup', 'hazelnut syrup', 'ice', 'whipped cream', 'honey', 'almond milk', 'soy milk', 'coconut milk', 'mint', 'lemon juice']
 *           description: Material category
 *         employee_id:
 *           type: integer
 *           description: Reference to employee who imported
 *         supplier_name:
 *           type: string
 *           description: Supplier name (read-only)
 *         employee_name:
 *           type: string
 *           description: Employee name (read-only)
 *
 *     MaterialObject:
 *       type: object
 *       required:
 *         - supplier_id
 *         - name_material
 *         - quantity
 *         - unit_price
 *         - category
 *         - employee_id
 *       properties:
 *         material_object_id:
 *           type: integer
 *           description: Auto-generated material object ID
 *         supplier_id:
 *           type: integer
 *           description: Reference to supplier
 *         name_material:
 *           type: string
 *           maxLength: 250
 *           description: Material name
 *         quantity:
 *           type: integer
 *           minimum: 1
 *           description: Material quantity
 *         unit_price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *           description: Unit price
 *         import_date:
 *           type: string
 *           format: date-time
 *           description: Import timestamp
 *         category:
 *           type: string
 *           enum: ['tissue', 'plastic cup', 'paper cup', 'cup lid', 'straw', 'plastic bag', 'napkin', 'glove', 'mask', 'tray', 'cleaning spray', 'dishwashing liquid', 'cloth towel', 'toilet paper', 'garbage bag', 'ice bucket', 'measuring spoon', 'milk frother', 'filter paper', 'receipt roll']
 *           description: Material category
 *         employee_id:
 *           type: integer
 *           description: Reference to employee who imported
 *         supplier_name:
 *           type: string
 *           description: Supplier name (read-only)
 *         employee_name:
 *           type: string
 *           description: Employee name (read-only)
 */

/**
 * @swagger
 * /api/materials/eating:
 *   get:
 *     summary: Get all eating materials with pagination and filtering
 *     tags: [Materials]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of materials per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by material name
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: supplier_id
 *         schema:
 *           type: integer
 *         description: Filter by supplier ID
 *     responses:
 *       200:
 *         description: Eating materials retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/MaterialEating'
 *                 pagination:
 *                   type: object
 */
router.get('/eating', MaterialController.getAllEatingMaterials);

/**
 * @swagger
 * /api/materials/low-stock:
 *   get:
 *     summary: Get low stock materials (quantity <= 10)
 *     tags: [Materials]
 *     responses:
 *       200:
 *         description: Low stock materials retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                         enum: [eating, object]
 *                       id:
 *                         type: integer
 *                       name_material:
 *                         type: string
 *                       quantity:
 *                         type: integer
 *                       category:
 *                         type: string
 *                       supplier_name:
 *                         type: string
 */
router.get('/low-stock', MaterialController.getLowStockMaterials);

/**
 * @swagger
 * /api/materials/inventory:
 *   get:
 *     summary: Get material inventory summary
 *     tags: [Materials]
 *     responses:
 *       200:
 *         description: Inventory summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                         enum: [eating, object]
 *                       name_material:
 *                         type: string
 *                       quantity:
 *                         type: integer
 */
router.get('/inventory', MaterialController.getInventorySummary);

/**
 * @swagger
 * /api/materials/eating/{id}:
 *   get:
 *     summary: Get eating material by ID
 *     tags: [Materials]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Material eating ID
 *     responses:
 *       200:
 *         description: Eating material retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/MaterialEating'
 *       404:
 *         description: Eating material not found
 */
router.get('/eating/:id', MaterialController.getEatingMaterialById);

/**
 * @swagger
 * /api/materials/object:
 *   get:
 *     summary: Get all object materials with pagination and filtering
 *     tags: [Materials]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of materials per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by material name
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: supplier_id
 *         schema:
 *           type: integer
 *         description: Filter by supplier ID
 *     responses:
 *       200:
 *         description: Object materials retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/MaterialObject'
 *                 pagination:
 *                   type: object
 */
router.get('/object', MaterialController.getAllObjectMaterials);

/**
 * @swagger
 * /api/materials/object/{id}:
 *   get:
 *     summary: Get object material by ID
 *     tags: [Materials]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Material object ID
 *     responses:
 *       200:
 *         description: Object material retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/MaterialObject'
 *       404:
 *         description: Object material not found
 */
router.get('/object/:id', MaterialController.getObjectMaterialById);

/**
 * @swagger
 * /api/materials/eating:
 *   post:
 *     summary: Create new eating material
 *     tags: [Materials]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - supplier_id
 *               - name_material
 *               - quantity
 *               - unit_price
 *               - category
 *               - employee_id
 *             properties:
 *               supplier_id:
 *                 type: integer
 *               name_material:
 *                 type: string
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *               unit_price:
 *                 type: number
 *                 minimum: 0.01
 *               category:
 *                 type: string
 *               employee_id:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Eating material created successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Supplier or employee not found
 */
router.post('/eating', MaterialController.createEatingMaterial);

/**
 * @swagger
 * /api/materials/object:
 *   post:
 *     summary: Create new object material
 *     tags: [Materials]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - supplier_id
 *               - name_material
 *               - quantity
 *               - unit_price
 *               - category
 *               - employee_id
 *             properties:
 *               supplier_id:
 *                 type: integer
 *               name_material:
 *                 type: string
 *               quantity:
 *                 type: integer
 *                 minimum: 1
 *               unit_price:
 *                 type: number
 *                 minimum: 0.01
 *               category:
 *                 type: string
 *               employee_id:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Object material created successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Supplier or employee not found
 */
router.post('/object', MaterialController.createObjectMaterial);

/**
 * @swagger
 * /api/materials/eating/{id}:
 *   put:
 *     summary: Update eating material
 *     tags: [Materials]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Material eating ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 0
 *               unit_price:
 *                 type: number
 *                 minimum: 0.01
 *     responses:
 *       200:
 *         description: Eating material updated successfully
 *       404:
 *         description: Eating material not found
 */
router.put('/eating/:id', MaterialController.updateEatingMaterial);

/**
 * @swagger
 * /api/materials/object/{id}:
 *   put:
 *     summary: Update object material
 *     tags: [Materials]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Material object ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 0
 *               unit_price:
 *                 type: number
 *                 minimum: 0.01
 *     responses:
 *       200:
 *         description: Object material updated successfully
 *       404:
 *         description: Object material not found
 */
router.put('/object/:id', MaterialController.updateObjectMaterial);

export default router;
