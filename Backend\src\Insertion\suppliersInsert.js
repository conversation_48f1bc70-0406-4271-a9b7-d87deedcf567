import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertSuppliers() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Delete all records before inserting new ones
    await connection.query('DELETE FROM suppliers');
    const suppliers = [];
    const emails = new Set();
    const categories = ['eating material', 'material object'];
    while (suppliers.length < 50) {
      const suppliesName = faker.company.name();
      // Always generate a unique email for each supplier
      let contactEmail = faker.internet.email() + '.' + faker.string.uuid();
      if (emails.has(contactEmail)) continue;
      emails.add(contactEmail);
      // Generate phone number as integer (no leading zero), max 9 digits
      let phoneStr = faker.phone.number('#########').replace(/\D/g, '').slice(0, 9);
      let phone = Number(phoneStr);
      const locationUrl = faker.location.streetAddress();
      const category = categories[Math.floor(Math.random() * categories.length)];
      suppliers.push([
        suppliesName,
        contactEmail,
        phone,
        locationUrl,
        category
      ]);
    }
    const sql = `INSERT INTO suppliers (supplies_name, contact_email, phone_number, location_url, category) VALUES ?`;
    await connection.query(sql, [suppliers]);
    console.log('Inserted 50 suppliers successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertSuppliers();
