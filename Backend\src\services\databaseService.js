import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';

/**
 * Database service layer for handling all database operations
 * Uses the existing ConnectSQlWithAiven.js connection
 */

class DatabaseService {
    /**
     * Execute a query with parameters
     * @param {string} query - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Array>} Query results
     */
    static async executeQuery(query, params = []) {
        let connection;
        try {
            connection = await connectToAivenDB();
            const [rows] = await connection.execute(query, params);
            return rows;
        } catch (error) {
            console.error('Database query error:', error);
            throw error;
        } finally {
            if (connection) {
                connection.release();
            }
        }
    }

    /**
     * Execute a query and return the first row
     * @param {string} query - SQL query
     * @param {Array} params - Query parameters
     * @returns {Promise<Object|null>} First row or null
     */
    static async executeQuerySingle(query, params = []) {
        const rows = await this.executeQuery(query, params);
        return rows.length > 0 ? rows[0] : null;
    }

    /**
     * Insert a record and return the inserted ID
     * @param {string} query - INSERT query
     * @param {Array} params - Query parameters
     * @returns {Promise<number>} Inserted ID
     */
    static async executeInsert(query, params = []) {
        let connection;
        try {
            connection = await connectToAivenDB();
            const [result] = await connection.execute(query, params);
            return result.insertId;
        } catch (error) {
            console.error('Database insert error:', error);
            throw error;
        } finally {
            if (connection) {
                connection.release();
            }
        }
    }

    /**
     * Execute an update/delete query and return affected rows
     * @param {string} query - UPDATE/DELETE query
     * @param {Array} params - Query parameters
     * @returns {Promise<number>} Number of affected rows
     */
    static async executeUpdate(query, params = []) {
        let connection;
        try {
            connection = await connectToAivenDB();
            const [result] = await connection.execute(query, params);
            return result.affectedRows;
        } catch (error) {
            console.error('Database update error:', error);
            throw error;
        } finally {
            if (connection) {
                connection.release();
            }
        }
    }

    /**
     * Execute multiple queries in a transaction
     * @param {Array} queries - Array of {query, params} objects
     * @returns {Promise<Array>} Array of results
     */
    static async executeTransaction(queries) {
        let connection;
        try {
            connection = await connectToAivenDB();
            await connection.beginTransaction();
            
            const results = [];
            for (const { query, params = [] } of queries) {
                const [result] = await connection.execute(query, params);
                results.push(result);
            }
            
            await connection.commit();
            return results;
        } catch (error) {
            if (connection) {
                await connection.rollback();
            }
            console.error('Database transaction error:', error);
            throw error;
        } finally {
            if (connection) {
                connection.release();
            }
        }
    }

    /**
     * Check if a record exists
     * @param {string} table - Table name
     * @param {string} column - Column name
     * @param {any} value - Value to check
     * @returns {Promise<boolean>} True if exists
     */
    static async recordExists(table, column, value) {
        const query = `SELECT 1 FROM ${table} WHERE ${column} = ? LIMIT 1`;
        const result = await this.executeQuerySingle(query, [value]);
        return result !== null;
    }

    /**
     * Get paginated results
     * @param {string} baseQuery - Base SELECT query without LIMIT
     * @param {Array} params - Query parameters
     * @param {number} page - Page number (1-based)
     * @param {number} limit - Records per page
     * @returns {Promise<Object>} Paginated results with metadata
     */
    static async getPaginatedResults(baseQuery, params = [], page = 1, limit = 10) {
        // Ensure page and limit are integers
        page = parseInt(page);
        limit = parseInt(limit);

        // Get total count
        const countQuery = `SELECT COUNT(*) as total FROM (${baseQuery}) as count_table`;
        const countResult = await this.executeQuerySingle(countQuery, params);
        const total = countResult ? countResult.total : 0;

        // Get paginated data
        const offset = (page - 1) * limit;
        // Use direct integer values in the query instead of parameters
        const paginatedQuery = `${baseQuery} LIMIT ${limit} OFFSET ${offset}`;
        const data = await this.executeQuery(paginatedQuery, params);

        return {
            data,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1
            }
        };
    }
}

export default DatabaseService;
