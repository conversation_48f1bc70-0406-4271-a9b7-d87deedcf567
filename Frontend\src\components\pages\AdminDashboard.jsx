import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Coffee, Users, ShoppingBag, DollarSign, Activity, Shield, Settings,
    LogOut, Menu, X, TrendingUp, AlertTriangle, Eye, Clock, Star,
    BarChart3, <PERSON><PERSON>hart, LineChart, Calendar, Filter, Search, Bell
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { adminAPI } from '../services/api';

const AdminDashboard = () => {
    const { user, logout } = useAuth();
    const [sidebarOpen, setSidebarOpen] = useState(true);
    const [activeTab, setActiveTab] = useState('dashboard');
    const [dashboardData, setDashboardData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [notifications, setNotifications] = useState([

        { id: 1, message: "New order received", time: "2 min ago", type: "info" },
        { id: 2, message: "Low stock alert", time: "5 min ago", type: "warning" },
        { id: 3, message: "Payment processed", time: "10 min ago", type: "success" }
    ]);

    // Gradient classes per color for Tailwind (avoids dynamic class issue)
    const colorGradients = {
        emerald: 'bg-gradient-to-r from-emerald-500 to-emerald-600',
        blue: 'bg-gradient-to-r from-blue-500 to-blue-600',
        purple: 'bg-gradient-to-r from-purple-500 to-purple-600',
        orange: 'bg-gradient-to-r from-orange-500 to-orange-600',
        pink: 'bg-gradient-to-r from-pink-500 to-pink-600',
        red: 'bg-gradient-to-r from-red-500 to-red-600',
        indigo: 'bg-gradient-to-r from-indigo-500 to-indigo-600',
        gray: 'bg-gradient-to-r from-gray-500 to-gray-600',
    };

    // Sidebar navigation items
    const navItems = [
        { id: 'dashboard', label: 'Dashboard', icon: BarChart3, color: 'emerald' },
        { id: 'users', label: 'Users', icon: Users, color: 'blue' },
        { id: 'employees', label: 'Employees', icon: Users, color: 'purple' },
        { id: 'orders', label: 'Orders', icon: ShoppingBag, color: 'orange' },
        { id: 'analytics', label: 'Analytics', icon: TrendingUp, color: 'pink' },
        { id: 'security', label: 'Security', icon: Shield, color: 'red' },
        { id: 'activity', label: 'Activity Logs', icon: Activity, color: 'indigo' },
        { id: 'settings', label: 'Settings', icon: Settings, color: 'gray' },
        { id: 'logout', label: 'Logout', icon: LogOut, color: 'red', action: 'logout' },
    ];


    // Load dashboard data
    useEffect(() => {
        const loadDashboardData = async () => {
            try {
                setLoading(true);
                const response = await adminAPI.getDashboardStats();
                if (response.data.success) {
                    const { counts, recentActivities, revenueData } = response.data.data;
                    const totalRevenue = revenueData.reduce((sum, r) => sum + Number(r.revenue || 0), 0);

                    setDashboardData({
                        stats: {
                            totalRevenue,
                            totalOrders: counts.orders,
                            totalCustomers: counts.customers,
                            totalEmployees: counts.employees
                        },
                        recentActivity: recentActivities.map(act => ({
                            action: `${act.operation_type} ${act.table_name}`,
                            user: act.user_name,
                            time: new Date(act.operation_time).toLocaleString()
                        })),
                        chartData: {
                            revenue: revenueData.slice(-7).map(r => r.revenue),
                            orders: [] // Placeholder if needed
                        }
                    });
                }
            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            } finally {
                setLoading(false);
            }
        };

        loadDashboardData();
    }, []);

    const handleLogout = () => {
        logout();
    };

    return (
        <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50">
            {/* Sidebar */}
            <AnimatePresence>
                {sidebarOpen && (
                    <motion.div
                        initial={{ x: -320 }}
                        animate={{ x: 0 }}
                        exit={{ x: -320 }}
                        transition={{ type: "tween", duration: 0.25, ease: [0.25, 0.8, 0.25, 1] }}
                        className="fixed left-0 top-0 h-full w-80 bg-white/95 backdrop-blur-lg shadow-2xl border-r border-emerald-100 z-50"
                    >
                        {/* Logo Section */}
                        <div className="p-6 border-b border-emerald-100 cursor-pointer" onClick={() => setSidebarOpen(prev => !prev)}>
                            <div className="flex items-center space-x-3">
                                <div className="w-12 h-12 bg-gradient-to-br from-emerald-600 to-green-700 rounded-full flex items-center justify-center">
                                    <Coffee className="w-6 h-6 text-white" />
                                </div>
                                <div>
                                    <h1 className="text-xl font-bold bg-gradient-to-r from-emerald-700 to-green-800 bg-clip-text text-transparent">
                                        Coffee Manager
                                    </h1>
                                    <p className="text-sm text-emerald-600">Admin Portal</p>
                                </div>
                            </div>
                        </div>

                        {/* User Profile */}
                        <div className="p-6 bg-gradient-to-r from-emerald-50 to-green-50 border-b border-emerald-100">
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-emerald-600 to-green-700 rounded-full flex items-center justify-center">
                                    <span className="text-white font-semibold text-sm">
                                        {user?.firstName?.[0]}{user?.lastName?.[0]}
                                    </span>
                                </div>
                                <div>
                                    <p className="font-semibold text-gray-800">{user?.firstName} {user?.lastName}</p>
                                    <p className="text-sm text-emerald-600 capitalize">{user?.role}</p>
                                </div>
                            </div>
                        </div>

                        {/* Navigation */}
                        <nav className="p-4 space-y-2 max-h-96 overflow-y-auto">
                            {navItems.map((item) => (
                                <motion.button
                                    key={item.id}
                                    onClick={() => item.id === 'logout' ? handleLogout() : setActiveTab(item.id)}
                                    className={`w-full flex items-center space-x-3 p-3 rounded-xl transition-all duration-200 ${
                                        activeTab === item.id
                                            ? `${colorGradients[item.color]} text-white shadow-lg`
                                            : 'text-gray-600 hover:bg-gray-50'
                                    }`}
                                    whileHover={{ scale: 1.02 }}
                                    whileTap={{ scale: 0.98 }}
                                >
                                    <item.icon className="w-5 h-5" />
                                    <span className="font-medium">{item.label}</span>
                                </motion.button>
                            ))}
                        </nav>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Floating sidebar toggle button (visible when sidebar is hidden) */}
            {!sidebarOpen && (
                <button
                    onClick={() => setSidebarOpen(true)}
                    className="fixed left-4 top-4 p-3 bg-emerald-600 text-white rounded-full shadow-lg z-50 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-transform duration-300 hover:scale-110"
                >
                    <Menu className="w-5 h-5" />
                </button>
            )}

            {/* Main Content */}
            <div className={`transition-all duration-500 ease-in-out ${sidebarOpen ? 'ml-80' : 'ml-0'}`}>
                {/* Header */}
                <header className="bg-white/90 backdrop-blur-lg shadow-sm border-b border-emerald-100 sticky top-0 z-40">
                    <div className="flex items-center justify-between px-6 py-4">
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => setSidebarOpen(!sidebarOpen)}
                                className="p-2 hover:bg-emerald-50 rounded-lg transition-colors"
                            >
                                {sidebarOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
                            </button>
                            <div>
                                <h2 className="text-2xl font-bold text-gray-800 capitalize">{activeTab}</h2>
                                <p className="text-emerald-600">Welcome back, {user?.firstName}!</p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            {/* Search */}
                            <div className="relative hidden md:block">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                                />
                            </div>

                            {/* Notifications */}
                            <div className="relative">
                                <button className="p-2 hover:bg-emerald-50 rounded-lg transition-colors relative">
                                    <Bell className="w-6 h-6 text-gray-600" />
                                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                        {notifications.length}
                                    </span>
                                </button>
                            </div>

                            {/* Profile */}
                            <div className="w-8 h-8 bg-gradient-to-br from-emerald-600 to-green-700 rounded-full flex items-center justify-center">
                                <span className="text-white font-semibold text-sm">
                                    {user?.firstName?.[0]}
                                </span>
                            </div>
                        </div>
                    </div>
                </header>

                {/* Dashboard Content */}
                <main className="p-6">
                    {loading ? (
                        <div className="flex items-center justify-center h-64">
                            <div className="flex items-center space-x-2">
                                <div className="w-8 h-8 border-4 border-emerald-500 border-t-transparent rounded-full animate-spin"></div>
                                <span className="text-emerald-600 font-medium">Loading dashboard...</span>
                            </div>
                        </div>
                    ) : (
                        <DashboardContent activeTab={activeTab} data={dashboardData} />
                    )}
                </main>
            </div>
        </div>
    );
};

// Dashboard Content Component
const DashboardContent = ({ activeTab, data }) => {
    if (activeTab === 'dashboard') {
        return <DashboardOverview data={data} />;
    }

    return (
        <div className="text-center py-20">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Coffee className="w-8 h-8 text-emerald-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Section
            </h3>
            <p className="text-gray-600">This section is coming soon!</p>
        </div>
    );
};

// Dashboard Overview Component
const DashboardOverview = ({ data }) => {
    const statCards = [
        {
            title: 'Total Revenue',
            value: `$${data?.stats?.totalRevenue?.toLocaleString() || '0'}`,
            icon: DollarSign,
            color: 'emerald',
            change: '+12.5%'
        },
        {
            title: 'Total Orders',
            value: data?.stats?.totalOrders?.toLocaleString() || '0',
            icon: ShoppingBag,
            color: 'blue',
            change: '+8.3%'
        },
        {
            title: 'Customers',
            value: data?.stats?.totalCustomers?.toLocaleString() || '0',
            icon: Users,
            color: 'purple',
            change: '+15.2%'
        },
        {
            title: 'Employees',
            value: data?.stats?.totalEmployees?.toLocaleString() || '0',
            icon: Users,
            color: 'orange',
            change: '+2.1%'
        }
    ];

    return (
        <div className="space-y-6">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {statCards.map((stat, index) => (
                    <motion.div
                        key={stat.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/50 hover:shadow-xl transition-all duration-300"
                    >
                        <div className="flex items-center justify-between mb-4">
                            <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                                <stat.icon className="w-6 h-6 text-white" />
                            </div>
                            <span className="text-green-600 text-sm font-semibold bg-green-50 px-2 py-1 rounded-lg">
                                {stat.change}
                            </span>
                        </div>
                        <h3 className="text-2xl font-bold text-gray-800 mb-1">{stat.value}</h3>
                        <p className="text-gray-600 text-sm">{stat.title}</p>
                    </motion.div>
                ))}
            </div>

            {/* Charts and Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Revenue Chart */}
                <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                    className="lg:col-span-2 bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/50"
                >
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-lg font-semibold text-gray-800">Revenue Overview</h3>
                        <div className="flex items-center space-x-2">
                            <Filter className="w-4 h-4 text-gray-400" />
                            <select className="text-sm border border-gray-200 rounded-lg px-3 py-1">
                                <option>Last 7 days</option>
                                <option>Last 30 days</option>
                                <option>Last 3 months</option>
                            </select>
                        </div>
                    </div>
                    <div className="h-64 flex items-end justify-between space-x-2">
                        {data?.chartData?.revenue?.map((value, index) => (
                            <motion.div
                                key={index}
                                initial={{ height: 0 }}
                                animate={{ height: `${(value / Math.max(...data.chartData.revenue)) * 100}%` }}
                                transition={{ delay: 0.5 + index * 0.1 }}
                                className="bg-gradient-to-t from-emerald-500 to-emerald-400 rounded-t-lg flex-1 min-h-2"
                            />
                        ))}
                    </div>
                </motion.div>

                {/* Recent Activity */}
                <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    className="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/50"
                >
                    <h3 className="text-lg font-semibold text-gray-800 mb-6">Recent Activity</h3>
                    <div className="space-y-4">
                        {data?.recentActivity?.map((activity, index) => (
                            <motion.div
                                key={index}
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: 0.7 + index * 0.1 }}
                                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-emerald-50 transition-colors"
                            >
                                <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                                    <Activity className="w-4 h-4 text-emerald-600" />
                                </div>
                                <div className="flex-1">
                                    <p className="text-sm font-medium text-gray-800">{activity.action}</p>
                                    <p className="text-xs text-gray-500">{activity.user} • {activity.time}</p>
                                </div>
                            </motion.div>
                        ))}
                    </div>
                </motion.div>
            </div>
        </div>
    );
};

export default AdminDashboard;
