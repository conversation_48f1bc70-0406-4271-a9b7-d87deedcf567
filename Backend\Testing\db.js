import { connectToAivenDB } from '../src/config/database.js';

async function testDatabase() {
    try {
        console.log('🔍 Testing database connection...');
        
        const connection = await connectToAivenDB();
        console.log('✅ Connected to Aiven MySQL database successfully!');
        
        // Test query
        const [rows] = await connection.execute('SELECT 1 + 1 AS solution, NOW() as current_time');
        console.log('📊 Test query result:', rows);
        
        // Test if activity_log table exists
        try {
            const [tables] = await connection.execute('SHOW TABLES LIKE "activity_log"');
            if (tables.length > 0) {
                console.log('✅ activity_log table exists');
                
                // Get recent activity logs
                const [logs] = await connection.execute('SELECT * FROM activity_log ORDER BY timestamp DESC LIMIT 5');
                console.log('📋 Recent activity logs:', logs.length, 'entries');
            } else {
                console.log('⚠️ activity_log table does not exist');
            }
        } catch (error) {
            console.log('⚠️ Could not check activity_log table:', error.message);
        }
        
        connection.release();
        console.log('🔌 Database connection closed');
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        process.exit(1);
    }
}

testDatabase();
