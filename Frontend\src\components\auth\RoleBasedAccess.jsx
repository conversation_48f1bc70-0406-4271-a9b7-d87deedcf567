import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

/**
 * Role-based access control component
 * Shows content only if user has required role(s)
 */
const RoleBasedAccess = ({ 
    allowedRoles = [], 
    children, 
    fallback = null,
    requireAll = false 
}) => {
    const { user, isAuthenticated } = useAuth();

    // If not authenticated, don't show content
    if (!isAuthenticated || !user) {
        return fallback;
    }

    // If no roles specified, show content to all authenticated users
    if (allowedRoles.length === 0) {
        return children;
    }

    // Check if user has required role(s)
    const userRole = user.role;
    const hasAccess = requireAll 
        ? allowedRoles.every(role => userRole === role)
        : allowedRoles.includes(userRole);

    return hasAccess ? children : fallback;
};

/**
 * Admin-only access component
 */
export const AdminOnly = ({ children, fallback = null }) => (
    <RoleBasedAccess allowedRoles={['admin']} fallback={fallback}>
        {children}
    </RoleBasedAccess>
);

/**
 * Manager and above access component
 */
export const ManagerAndAbove = ({ children, fallback = null }) => (
    <RoleBasedAccess allowedRoles={['admin', 'manager']} fallback={fallback}>
        {children}
    </RoleBasedAccess>
);

/**
 * Staff access component (manager, cashier)
 */
export const StaffOnly = ({ children, fallback = null }) => (
    <RoleBasedAccess allowedRoles={['admin', 'manager', 'cashier']} fallback={fallback}>
        {children}
    </RoleBasedAccess>
);

/**
 * Hook to check user permissions
 */
export const usePermissions = () => {
    const { user, isAuthenticated } = useAuth();

    const hasRole = (role) => {
        return isAuthenticated && user && user.role === role;
    };

    const hasAnyRole = (roles) => {
        return isAuthenticated && user && roles.includes(user.role);
    };

    const isAdmin = () => hasRole('admin');
    const isManager = () => hasRole('manager');
    const isCashier = () => hasRole('cashier');
    const isUser = () => hasRole('user');

    const canManageUsers = () => isAdmin();
    const canViewReports = () => hasAnyRole(['admin', 'manager']);
    const canProcessOrders = () => hasAnyRole(['admin', 'manager', 'cashier']);
    const canViewInventory = () => hasAnyRole(['admin', 'manager']);

    return {
        hasRole,
        hasAnyRole,
        isAdmin,
        isManager,
        isCashier,
        isUser,
        canManageUsers,
        canViewReports,
        canProcessOrders,
        canViewInventory
    };
};

export default RoleBasedAccess;
