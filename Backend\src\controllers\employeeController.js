import DatabaseService from '../services/databaseService.js';

/**
 * Employee Controller
 * Handles all employee-related operations
 */

class EmployeeController {
    /**
     * Get all employees with pagination and filtering
     */
    static async getAllEmployees(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search || '';
            const position = req.query.position || '';

            let baseQuery = `
                SELECT 
                    employee_id,
                    first_name,
                    last_name,
                    position,
                    hire_date,
                    salary,
                    phone_number,
                    email,
                    address
                FROM employees
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (search) {
                whereConditions.push(`(
                    first_name LIKE ? OR 
                    last_name LIKE ? OR 
                    email LIKE ?
                )`);
                const searchParam = `%${search}%`;
                params.push(searchParam, searchParam, searchParam);
            }
            
            if (position) {
                whereConditions.push('position = ?');
                params.push(position);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY hire_date DESC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Employees retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting employees:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve employees',
                error: error.message
            });
        }
    }

    /**
     * Get employee by ID
     */
    static async getEmployeeById(req, res) {
        try {
            const { id } = req.params;
            
            const employee = await DatabaseService.executeQuerySingle(
                `SELECT 
                    employee_id,
                    first_name,
                    last_name,
                    position,
                    hire_date,
                    salary,
                    phone_number,
                    email,
                    address
                FROM employees 
                WHERE employee_id = ?`,
                [id]
            );

            if (!employee) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found'
                });
            }

            res.json({
                success: true,
                message: 'Employee retrieved successfully',
                data: employee
            });
        } catch (error) {
            console.error('Error getting employee:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve employee',
                error: error.message
            });
        }
    }

    /**
     * Create new employee
     */
    static async createEmployee(req, res) {
        try {
            const { 
                first_name, 
                last_name, 
                position, 
                hire_date, 
                salary, 
                phone_number, 
                email, 
                address 
            } = req.body;

            // Validation
            if (!first_name || !last_name || !position || !hire_date || !salary || !phone_number || !email || !address) {
                return res.status(400).json({
                    success: false,
                    message: 'All fields are required'
                });
            }

            // Validate position enum
            const validPositions = ['Manager', 'Cashier', 'Vendor', 'Barista'];
            if (!validPositions.includes(position)) {
                return res.status(400).json({
                    success: false,
                    message: `Position must be one of: ${validPositions.join(', ')}`
                });
            }

            // Validate salary is positive
            if (salary <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Salary must be greater than 0'
                });
            }

            // Check if email already exists
            const existingEmployee = await DatabaseService.recordExists('employees', 'email', email);
            if (existingEmployee) {
                return res.status(409).json({
                    success: false,
                    message: 'Employee with this email already exists'
                });
            }

            const employeeId = await DatabaseService.executeInsert(
                `INSERT INTO employees (first_name, last_name, position, hire_date, salary, phone_number, email, address) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [first_name, last_name, position, hire_date, salary, phone_number, email, address]
            );

            const newEmployee = await DatabaseService.executeQuerySingle(
                `SELECT 
                    employee_id,
                    first_name,
                    last_name,
                    position,
                    hire_date,
                    salary,
                    phone_number,
                    email,
                    address
                FROM employees 
                WHERE employee_id = ?`,
                [employeeId]
            );

            res.status(201).json({
                success: true,
                message: 'Employee created successfully',
                data: newEmployee
            });
        } catch (error) {
            console.error('Error creating employee:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create employee',
                error: error.message
            });
        }
    }

    /**
     * Update employee
     */
    static async updateEmployee(req, res) {
        try {
            const { id } = req.params;
            const { 
                first_name, 
                last_name, 
                position, 
                hire_date, 
                salary, 
                phone_number, 
                email, 
                address 
            } = req.body;

            // Check if employee exists
            const existingEmployee = await DatabaseService.recordExists('employees', 'employee_id', id);
            if (!existingEmployee) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found'
                });
            }

            // Validate position enum if provided
            if (position) {
                const validPositions = ['Manager', 'Cashier', 'Vendor', 'Barista'];
                if (!validPositions.includes(position)) {
                    return res.status(400).json({
                        success: false,
                        message: `Position must be one of: ${validPositions.join(', ')}`
                    });
                }
            }

            // Validate salary if provided
            if (salary !== undefined && salary <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Salary must be greater than 0'
                });
            }

            // Check if email is being changed and if it already exists
            if (email) {
                const emailExists = await DatabaseService.executeQuerySingle(
                    'SELECT employee_id FROM employees WHERE email = ? AND employee_id != ?',
                    [email, id]
                );
                if (emailExists) {
                    return res.status(409).json({
                        success: false,
                        message: 'Email already exists for another employee'
                    });
                }
            }

            const affectedRows = await DatabaseService.executeUpdate(
                `UPDATE employees 
                 SET first_name = ?, last_name = ?, position = ?, hire_date = ?, 
                     salary = ?, phone_number = ?, email = ?, address = ?
                 WHERE employee_id = ?`,
                [first_name, last_name, position, hire_date, salary, phone_number, email, address, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found or no changes made'
                });
            }

            const updatedEmployee = await DatabaseService.executeQuerySingle(
                `SELECT 
                    employee_id,
                    first_name,
                    last_name,
                    position,
                    hire_date,
                    salary,
                    phone_number,
                    email,
                    address
                FROM employees 
                WHERE employee_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Employee updated successfully',
                data: updatedEmployee
            });
        } catch (error) {
            console.error('Error updating employee:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update employee',
                error: error.message
            });
        }
    }

    /**
     * Delete employee
     */
    static async deleteEmployee(req, res) {
        try {
            const { id } = req.params;

            // Check if employee has related records
            const hasPayments = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM payments WHERE employee_id = ? LIMIT 1',
                [id]
            );

            const hasMaterialEating = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM material_eating WHERE employee_id = ? LIMIT 1',
                [id]
            );

            const hasMaterialObject = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM material_object WHERE employee_id = ? LIMIT 1',
                [id]
            );

            if (hasPayments || hasMaterialEating || hasMaterialObject) {
                return res.status(409).json({
                    success: false,
                    message: 'Cannot delete employee with existing records (payments, materials)'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                'DELETE FROM employees WHERE employee_id = ?',
                [id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found'
                });
            }

            res.json({
                success: true,
                message: 'Employee deleted successfully'
            });
        } catch (error) {
            console.error('Error deleting employee:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete employee',
                error: error.message
            });
        }
    }

    /**
     * Get employees by position
     */
    static async getEmployeesByPosition(req, res) {
        try {
            const { position } = req.params;
            
            const validPositions = ['Manager', 'Cashier', 'Vendor', 'Barista'];
            if (!validPositions.includes(position)) {
                return res.status(400).json({
                    success: false,
                    message: `Position must be one of: ${validPositions.join(', ')}`
                });
            }

            const employees = await DatabaseService.executeQuery(
                `SELECT 
                    employee_id,
                    first_name,
                    last_name,
                    position,
                    hire_date,
                    salary,
                    phone_number,
                    email,
                    address
                FROM employees 
                WHERE position = ?
                ORDER BY hire_date DESC`,
                [position]
            );

            res.json({
                success: true,
                message: `${position} employees retrieved successfully`,
                data: employees
            });
        } catch (error) {
            console.error('Error getting employees by position:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve employees by position',
                error: error.message
            });
        }
    }
}

export default EmployeeController;
