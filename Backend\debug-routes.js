import express from 'express';

const app = express();

// Test each route file individually
const routeFiles = [
    './src/routes/customerRoutes.js',
    './src/routes/employeeRoutes.js', 
    './src/routes/supplierRoutes.js',
    './src/routes/materialRoutes.js',
    './src/routes/productRoutes.js',
    './src/routes/orderRoutes.js',
    './src/routes/paymentRoutes.js',
    './src/routes/analyticsRoutes.js'
];

for (const routeFile of routeFiles) {
    try {
        console.log(`Testing ${routeFile}...`);
        const routeModule = await import(routeFile);
        const testApp = express();
        testApp.use('/test', routeModule.default);
        console.log(`✅ ${routeFile} loaded successfully`);
    } catch (error) {
        console.error(`❌ Error in ${routeFile}:`, error.message);
        console.error('Stack:', error.stack);
        break;
    }
}

console.log('Route testing completed.');
