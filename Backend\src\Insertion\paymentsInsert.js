import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertPayments() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Get employee and order IDs for foreign keys
    const [employees] = await connection.query('SELECT employee_id FROM employees');
    const [orders] = await connection.query('SELECT order_id, unit_price, quantity FROM orders LIMIT 800000');
    if (employees.length === 0 || orders.length === 0) {
      throw new Error('Employees or orders table is empty. Please insert data first.');
    }
    const paymentTypes = ['ABA', 'Aceleda', 'Cash', 'Other'];
    const batchSize = 10000; // Insert in batches to avoid memory/timeout issues
    let totalInserted = 0;
    for (let batch = 0; batch < 80; batch++) {
      const payments = [];
      for (let i = 0; i < batchSize; i++) {
        const orderObj = orders[batch * batchSize + i];
        if (!orderObj) break;
        const employee = employees[Math.floor(Math.random() * employees.length)].employee_id;
        const orderId = orderObj.order_id;
        const paymentType = paymentTypes[Math.floor(Math.random() * paymentTypes.length)];
        // Use order's unit_price * quantity as amount_money for realism
        const amountMoney = (parseFloat(orderObj.unit_price) * parseInt(orderObj.quantity)).toFixed(2);
        payments.push([
          employee,
          orderId,
          paymentType,
          amountMoney
        ]);
      }
      const sql = `INSERT INTO payments (employee_id, order_id, payment_type, amount_money) VALUES ?`;
      await connection.query(sql, [payments]);
      totalInserted += payments.length;
      console.log(`Inserted ${totalInserted} payments so far...`);
    }
    console.log('Inserted 800,000 payments successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertPayments();
