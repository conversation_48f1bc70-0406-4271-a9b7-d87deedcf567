import express from 'express';
import { connectToAivenDB } from '../src/config/database.js';

const app = express();

// Test basic route
app.get('/', (req, res) => {
    res.json({ message: 'Basic route works' });
});

// Test simple parameter route
app.get('/test/:id', (req, res) => {
    res.json({ id: req.params.id });
});

// Test importing adminRoutes to see if that's the issue
console.log('About to import adminRoutes...');
try {
    const adminRoutes = await import('./adminRoutes.js');
    console.log('✅ adminRoutes imported successfully');
    
    // Try to use the routes
    app.use('/api/admin', adminRoutes.default);
    console.log('✅ adminRoutes registered successfully');
} catch (error) {
    console.error('❌ Error with adminRoutes:', error);
}

const PORT = 3001;
app.listen(PORT, () => {
    console.log(`🚀 Test server running on http://localhost:${PORT}`);
});
