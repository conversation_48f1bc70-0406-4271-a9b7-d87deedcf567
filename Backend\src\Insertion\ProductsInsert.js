import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertProducts() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Get material_eating and material_object IDs for foreign keys
    const [eatings] = await connection.query('SELECT material_eating_id FROM material_eating');
    const [objects] = await connection.query('SELECT material_object_id FROM material_object');
    if (eatings.length === 0 || objects.length === 0) {
      throw new Error('material_eating or material_object table is empty. Please insert data first.');
    }
    const categories = ['Coffee', 'Tea', 'Pastry', 'Sandwich', 'Juice', 'Other'];
    const products = [];
    for (let i = 0; i < 200; i++) {
      const productName = faker.commerce.productName();
      const materialEatingId = eatings[Math.floor(Math.random() * eatings.length)].material_eating_id;
      const materialObjectId = objects[Math.floor(Math.random() * objects.length)].material_object_id;
      const category = categories[Math.floor(Math.random() * categories.length)];
      const description = faker.commerce.productDescription();
      const unitPrice = faker.number.float({ min: 1, max: 50, precision: 0.01 });
      products.push([
        productName,
        materialEatingId,
        materialObjectId,
        category,
        description,
        unitPrice
      ]);
    }
    const sql = `INSERT INTO products (product_name, material_eating_id, material_object_id, category_name, description, unit_price) VALUES ?`;
    await connection.query(sql, [products]);
    console.log('Inserted 200 products successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertProducts();
