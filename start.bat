@echo off
echo ===================================
echo Starting Coffee Management System
echo ===================================
echo.

echo Starting Backend Server...
start "Backend Server" cmd /k "cd Backend && npm run dev"

timeout /t 3 /nobreak >nul

echo Starting Frontend Development Server...
start "Frontend Server" cmd /k "cd Frontend && npm run dev"

echo.
echo ===================================
echo Servers Starting...
echo ===================================
echo Backend: http://localhost:3000
echo Frontend: http://localhost:5173
echo Admin Panel: http://localhost:5173/admin
echo ===================================
echo.
echo Press any key to stop all servers...
pause >nul

echo Stopping servers...
taskkill /f /im node.exe 2>nul
echo Servers stopped.
pause
