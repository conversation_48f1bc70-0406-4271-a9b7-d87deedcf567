# Coffee Management System - Admin Interface

A complete full-stack application for managing coffee shop operations with an advanced admin interface.

## 🏗️ Project Structure

```
ProjectDB-Year2-Term3/
├── Backend/                 # Node.js/Express API
│   ├── config/             # Database configurations
│   ├── controllers/        # Route handlers
│   ├── routes/            # API routes
│   ├── server.js          # Main server file
│   └── package.json       # Backend dependencies
├── Frontend/              # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   │   ├── pages/     # Page components
│   │   │   └── services/  # API services
│   │   ├── App.jsx        # Main App component
│   │   └── main.jsx       # Entry point
│   └── package.json       # Frontend dependencies
└── modelDB/               # Database scripts
    ├── DDL.sql           # Database schema
    ├── AccessControl.sql  # User roles & permissions
    ├── AdvancedSecurity.sql # Security features
    ├── ManageAccessControl.sql # Management tools
    ├── TestAccessControl.sql # Testing scripts
    └── SampleData.sql    # Sample data for testing
```

## 🚀 Setup Instructions

### 1. Database Setup

1. **Create the Database:**
   ```sql
   mysql -u root -p
   ```
   ```sql
   CREATE DATABASE coffee_management_db;
   ```

2. **Run Database Scripts in Order:**
   ```bash
   # 1. Create tables and schema
   mysql -u root -p coffee_management_db < modelDB/DDL.sql
   
   # 2. Set up access control
   mysql -u root -p coffee_management_db < modelDB/AccessControl.sql
   
   # 3. Add advanced security features
   mysql -u root -p coffee_management_db < modelDB/AdvancedSecurity.sql
   
   # 4. Add management tools
   mysql -u root -p coffee_management_db < modelDB/ManageAccessControl.sql
   
   # 5. Insert sample data for testing
   mysql -u root -p coffee_management_db < modelDB/SampleData.sql
   ```

### 2. Backend Setup

1. **Navigate to Backend Directory:**
   ```bash
   cd Backend
   ```

2. **Install Dependencies:**
   ```bash
   npm install
   ```

3. **Update .env File:**
   The `.env` file is already configured for local development:
   ```env
   DBP_HOST=localhost
   DBP_USER=root
   DBP_PASSWORD=12345
   DBP_NAME=coffee_management_db
   DBP_PORT=3306
   PORT=3000
   NODE_ENV=development
   FRONTEND_URL=http://localhost:5173
   ```

4. **Start Backend Server:**
   ```bash
   npm run dev
   ```
   Server will run on `http://localhost:3000`

### 3. Frontend Setup

1. **Navigate to Frontend Directory:**
   ```bash
   cd Frontend
   ```

2. **Install Dependencies:**
   ```bash
   npm install
   ```

3. **Start Development Server:**
   ```bash
   npm run dev
   ```
   Frontend will run on `http://localhost:5173`

## 🔐 Access Control System

### User Roles & Permissions

| Role | Access Level | Permissions |
|------|-------------|-------------|
| **Admin** | Full System Access | • All database operations<br>• User management<br>• Security monitoring<br>• Audit logs |
| **Manager** | Business Operations | • Employee management<br>• All business data<br>• Reports and analytics<br>• Inventory management |
| **Cashier** | Order Processing | • Customer orders<br>• Payment processing<br>• Customer information<br>• Product catalog |
| **Barista** | Order Fulfillment | • View pending orders<br>• Update order status<br>• View products |
| **Vendor** | Inventory Management | • Supplier management<br>• Material inventory<br>• Stock updates |
| **Customer** | Self-Service | • View products<br>• Own order history<br>• Loyalty points |

### Default Users

The system creates these default users:

```
Admin Users:
- Username: db_admin, Password: DbAdmin123!

Manager Users:
- Username: manager1, Password: Manager123!
- Username: manager2, Password: Manager456!

Cashier Users:
- Username: cashier1, Password: Cashier123!
- Username: cashier2, Password: Cashier456!
- Username: cashier3, Password: Cashier789!

Barista Users:
- Username: barista1, Password: Barista123!
- Username: barista2, Password: Barista456!

Vendor Users:
- Username: vendor1, Password: Vendor123!
- Username: vendor2, Password: Vendor456!

Customer App:
- Username: customer_app, Password: CustomerApp123!

Backend Application:
- Username: app_backend, Password: AppBackend123!
```

## 📊 Admin Interface Features

### 1. Dashboard
- **Real-time Statistics:** Customer count, orders, revenue, etc.
- **Recent Activities:** Live audit trail of system operations
- **Revenue Analytics:** Daily revenue tracking for last 30 days
- **Quick Overview:** Key performance indicators

### 2. User Management
- **View All Users:** Complete list with status and role information
- **Create New Users:** Add users with specific roles and permissions
- **Account Management:** Lock/unlock accounts, deactivate users
- **Security Status:** Monitor failed login attempts and account security

### 3. Employee Management
- **Employee Directory:** Full employee information with contact details
- **Add/Edit Employees:** Complete CRUD operations for employee records
- **Role Assignment:** Manage employee positions and responsibilities
- **Salary Management:** Track and update employee compensation

### 4. Audit Logs
- **Complete Activity Trail:** Every database operation is logged
- **User Tracking:** See who performed what actions and when
- **Data Changes:** Track old and new values for updates
- **Security Monitoring:** Identify unusual activity patterns

### 5. Security Reports
- **User Status Summary:** Overview of active, inactive, and locked accounts
- **Risk Assessment:** Identify high-risk users with failed login attempts
- **Activity Patterns:** Analyze recent system usage
- **Violation Alerts:** Automatic detection of security issues

## 🔒 Security Features

### 1. Role-Based Access Control (RBAC)
- Granular permissions for each user role
- Database-level security enforcement
- Secure views with data filtering

### 2. Data Masking
- Automatic data masking based on user permissions
- Phone numbers and emails protected for lower-privilege users
- Salary information restricted to managers and admins

### 3. Audit Logging
- Comprehensive logging of all database operations
- Automatic tracking of data changes
- User activity monitoring

### 4. Account Security
- Failed login attempt tracking
- Automatic account locking after 3 failed attempts
- Password requirements and security policies

### 5. Row-Level Security
- Users can only access data appropriate to their role
- Dynamic data filtering based on security context
- Secure stored procedures for sensitive operations

## 🛠️ API Endpoints

### Admin Dashboard
- `GET /api/admin/dashboard/stats` - Get dashboard statistics
- `GET /api/admin/security-report` - Generate security report

### User Management
- `GET /api/admin/users` - Get all users
- `POST /api/admin/users` - Create new user
- `PATCH /api/admin/users/:username/deactivate` - Deactivate user
- `PATCH /api/admin/users/:username/unlock` - Unlock user account

### Employee Management
- `GET /api/admin/employees` - Get all employees
- `POST /api/admin/employees` - Create new employee
- `PUT /api/admin/employees/:id` - Update employee
- `DELETE /api/admin/employees/:id` - Delete employee

### Audit & Security
- `GET /api/admin/audit-logs` - Get audit logs with pagination
- `GET /api/admin/security-report` - Get security analysis

## 🧪 Testing

### Backend Testing
```bash
# Test database connection
curl http://localhost:3000/

# Test admin stats endpoint
curl http://localhost:3000/api/admin/dashboard/stats

# Test user list endpoint
curl http://localhost:3000/api/admin/users
```

### Database Testing
Run the test scripts to verify access control:
```bash
mysql -u manager1 -p coffee_management_db < modelDB/TestAccessControl.sql
```

### Security Testing
Test different user roles by connecting with different credentials:
```bash
# Test as cashier (limited access)
mysql -u cashier1 -pCashier123! coffee_management_db

# Test as barista (order management only)
mysql -u barista1 -pBarista123! coffee_management_db

# Test as vendor (inventory access)
mysql -u vendor1 -pVendor123! coffee_management_db
```

## 🔧 Development

### Adding New Features
1. **Backend:** Add new controllers in `Backend/controllers/`
2. **Routes:** Define new routes in `Backend/routes/`
3. **Frontend:** Create new components in `Frontend/src/components/`
4. **Database:** Add new tables/procedures in `modelDB/`

### Environment Variables
- **Backend:** Update `Backend/.env` for database and server config
- **Frontend:** Update `Frontend/.env` for API endpoints

### Security Considerations
- Always use parameterized queries
- Implement proper input validation
- Follow the principle of least privilege
- Regular security audits and monitoring

## 📈 Monitoring & Maintenance

### Security Monitoring
- Check audit logs regularly for unusual activity
- Monitor failed login attempts
- Review user access patterns

### Database Maintenance
- Regular backups of the database
- Monitor performance and optimize queries
- Update security policies as needed

### System Updates
- Keep dependencies updated
- Monitor for security vulnerabilities
- Regular testing of access controls

## 🆘 Troubleshooting

### Common Issues

1. **Database Connection Failed:**
   - Check MySQL service is running
   - Verify credentials in `.env` file
   - Ensure database exists

2. **Access Denied Errors:**
   - Verify user roles are properly assigned
   - Check if account is locked
   - Confirm permissions are granted

3. **Frontend Can't Connect to Backend:**
   - Ensure backend is running on port 3000
   - Check CORS settings in server configuration
   - Verify API URL in frontend `.env`

4. **Missing Tables/Data:**
   - Run database scripts in correct order
   - Check for SQL errors during execution
   - Verify all scripts completed successfully

## 📝 Notes

- The system uses MySQL for database management
- Frontend built with React and Tailwind CSS
- Backend uses Node.js and Express
- All passwords should be changed in production
- Sample data is included for testing purposes
- The system includes comprehensive audit logging
- Role-based access control is enforced at database level

## 🔮 Future Enhancements

- JWT-based authentication for web interface
- Real-time notifications for security events
- Advanced analytics and reporting
- Mobile app integration
- Inventory management automation
- Customer loyalty program features
- Multi-location support
- Integration with payment gateways

---

**🚀 Ready to Go!**

Your Coffee Management System with Admin Interface is now ready for use. The system provides enterprise-level security, comprehensive management tools, and a modern user interface for efficient coffee shop operations.
