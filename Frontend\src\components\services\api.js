import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Request interceptor for logging
api.interceptors.request.use(
    (config) => {
        // Attach Authorization header if token exists
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
    },
    (error) => {
        console.error('❌ Request error:', error);
        return Promise.reject(error);
    }
);

// Response interceptor for error handling
api.interceptors.response.use(
    (response) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
    },
    (error) => {
        console.error('❌ Response error:', error.response?.data || error.message);
        return Promise.reject(error);
    }
);

// Admin API functions
export const adminAPI = {
    // Authentication
    login: (credentials) => api.post('/auth/login', credentials),
    logout: () => api.post('/auth/logout'),
    validateToken: () => api.get('/auth/validate'),
    
    // Dashboard
    getDashboardStats: () => api.get('/admin/dashboard/stats'),
    
    // User Management
    getAllUsers: () => api.get('/admin/users'),
    createUser: (userData) => api.post('/admin/users', userData),
    deactivateUser: (username) => api.patch(`/admin/users/${username}/deactivate`),
    unlockUser: (username) => api.patch(`/admin/users/${username}/unlock`),
    
    // Employee Management
    getAllEmployees: () => api.get('/admin/employees'),
    createEmployee: (employeeData) => api.post('/admin/employees', employeeData),
    updateEmployee: (id, employeeData) => api.put(`/admin/employees/${id}`, employeeData),
    deleteEmployee: (id) => api.delete(`/admin/employees/${id}`),
    
    // Audit and Security
    getAuditLogs: (params = {}) => api.get('/admin/audit-logs', { params }),
    getSecurityReport: () => api.get('/admin/security-report'),
    
    // Activity Logs
    getActivityLogs: (params = {}) => api.get('/admin/activity-logs', { params }),
    getSecurityLogs: (params = {}) => api.get('/admin/security-logs', { params }),
    getApiUsageStats: (params = {}) => api.get('/admin/api-usage-stats', { params }),
    getSystemMetrics: () => api.get('/admin/system-metrics'),
    cleanOldLogs: (daysToKeep) => api.post('/admin/clean-logs', { daysToKeep }),
};

export default api;