import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080/api',
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
});

// Request interceptor for logging
api.interceptors.request.use(
    (config) => {
        // Attach Authorization header if token exists
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
    },
    (error) => {
        console.error('❌ Request error:', error);
        return Promise.reject(error);
    }
);

// Response interceptor for error handling
api.interceptors.response.use(
    (response) => {
        console.log(`✅ API Response: ${response.status} ${response.config.url}`);
        return response;
    },
    (error) => {
        console.error('❌ Response error:', error.response?.data || error.message);
        return Promise.reject(error);
    }
);

// API functions for the coffee management system
export const coffeeAPI = {
    // Dashboard data using available endpoints
    getDashboardStats: async () => {
        const [customers, employees, orders, analytics] = await Promise.all([
            api.get('/customers'),
            api.get('/employees'),
            api.get('/orders'),
            api.get('/analytics/sales/monthly')
        ]);
        
        return {
            data: {
                success: true,
                data: {
                    counts: {
                        customers: customers.data.pagination?.total || customers.data.data?.length || 0,
                        employees: employees.data.pagination?.total || employees.data.data?.length || 0,
                        orders: orders.data.pagination?.total || orders.data.data?.length || 0,
                        products: 0 // Will be populated later
                    },
                    recentActivities: [],
                    revenueData: analytics.data.data || []
                }
            }
        };
    },
    
    // Customer Management
    getAllCustomers: () => api.get('/customers'),
    getCustomerById: (id) => api.get(`/customers/${id}`),
    createCustomer: (customerData) => api.post('/customers', customerData),
    updateCustomer: (id, customerData) => api.put(`/customers/${id}`, customerData),
    deleteCustomer: (id) => api.delete(`/customers/${id}`),
    
    // Employee Management
    getAllEmployees: () => api.get('/employees'),
    getEmployeeById: (id) => api.get(`/employees/${id}`),
    createEmployee: (employeeData) => api.post('/employees', employeeData),
    updateEmployee: (id, employeeData) => api.put(`/employees/${id}`, employeeData),
    deleteEmployee: (id) => api.delete(`/employees/${id}`),
    
    // Order Management
    getAllOrders: () => api.get('/orders'),
    getOrderById: (id) => api.get(`/orders/${id}`),
    createOrder: (orderData) => api.post('/orders', orderData),
    updateOrder: (id, orderData) => api.put(`/orders/${id}`, orderData),
    deleteOrder: (id) => api.delete(`/orders/${id}`),
    
    // Product Management
    getAllProducts: () => api.get('/products'),
    getProductById: (id) => api.get(`/products/${id}`),
    createProduct: (productData) => api.post('/products', productData),
    updateProduct: (id, productData) => api.put(`/products/${id}`, productData),
    deleteProduct: (id) => api.delete(`/products/${id}`),
    
    // Analytics
    getSalesData: () => api.get('/analytics/sales/monthly'),
    getRevenueByProduct: () => api.get('/analytics/revenue/by-product'),
    getTopCustomers: () => api.get('/analytics/customers/top-spenders'),
    getMostSoldProducts: () => api.get('/analytics/products/most-sold'),
    getOrderStats: () => api.get('/analytics/orders/monthly-stats'),
    
    // Supplier Management
    getAllSuppliers: () => api.get('/suppliers'),
    
    // Payment Management
    getAllPayments: () => api.get('/payments'),
};

export default api;