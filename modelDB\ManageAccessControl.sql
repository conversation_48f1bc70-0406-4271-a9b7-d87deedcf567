-- -- =====================================================
-- -- ACCESS CONTROL MANAGEMENT AND MONITORING SCRIPT
-- -- =====================================================
-- -- This script provides tools for managing users, roles,
-- -- and monitoring security events in the coffee management system

-- USE coffee_management_db;

-- -- =====================================================
-- -- 1. USER MANAGEMENT PROCEDURES
-- -- =====================================================

-- DELIMITER //

-- -- Procedure to create a new employee user
-- CREATE PROCEDURE create_employee_user(
--     IN p_username VARCHAR(100),
--     IN p_password VARCHAR(100),
--     IN p_employee_id INT,
--     IN p_role_type ENUM('Manager', 'Cashier', 'Vendor', 'Barista'),
--     OUT p_result_message VARCHAR(255)
-- )
-- BEGIN
--     DECLARE v_role_name VARCHAR(50);
--     DECLARE v_sql_stmt TEXT;
--     DECLARE EXIT HANDLER FOR SQLEXCEPTION
--     BEGIN
--         ROLLBACK;
--         SET p_result_message = CONCAT('Error creating user: ', p_username);
--     END;
    
--     -- Map role type to role name
--     SET v_role_name = CASE p_role_type
--         WHEN 'Manager' THEN 'manager_role'
--         WHEN 'Cashier' THEN 'cashier_role'
--         WHEN 'Vendor' THEN 'vendor_role'
--         WHEN 'Barista' THEN 'barista_role'
--     END;
    
--     START TRANSACTION;
    
--     -- Create MySQL user
--     SET v_sql_stmt = CONCAT('CREATE USER ''', p_username, '''@''localhost'' IDENTIFIED BY ''', p_password, '''');
--     SET @sql = v_sql_stmt;
--     PREPARE stmt FROM @sql;
--     EXECUTE stmt;
--     DEALLOCATE PREPARE stmt;
    
--     -- Grant role
--     SET v_sql_stmt = CONCAT('GRANT ''', v_role_name, ''' TO ''', p_username, '''@''localhost''');
--     SET @sql = v_sql_stmt;
--     PREPARE stmt FROM @sql;
--     EXECUTE stmt;
--     DEALLOCATE PREPARE stmt;
    
--     -- Set default role
--     SET v_sql_stmt = CONCAT('SET DEFAULT ROLE ''', v_role_name, ''' TO ''', p_username, '''@''localhost''');
--     SET @sql = v_sql_stmt;
--     PREPARE stmt FROM @sql;
--     EXECUTE stmt;
--     DEALLOCATE PREPARE stmt;
    
--     -- Insert into security context
--     INSERT INTO user_security_context (user_name, employee_id, role_type)
--     VALUES (p_username, p_employee_id, p_role_type);
    
--     COMMIT;
--     FLUSH PRIVILEGES;
    
--     SET p_result_message = CONCAT('User ', p_username, ' created successfully with role ', p_role_type);
-- END //

-- -- Procedure to deactivate a user
-- CREATE PROCEDURE deactivate_user(
--     IN p_username VARCHAR(100),
--     OUT p_result_message VARCHAR(255)
-- )
-- BEGIN
--     DECLARE v_sql_stmt TEXT;
--     DECLARE EXIT HANDLER FOR SQLEXCEPTION
--     BEGIN
--         ROLLBACK;
--         SET p_result_message = CONCAT('Error deactivating user: ', p_username);
--     END;
    
--     START TRANSACTION;
    
--     -- Drop MySQL user
--     SET v_sql_stmt = CONCAT('DROP USER ''', p_username, '''@''localhost''');
--     SET @sql = v_sql_stmt;
--     PREPARE stmt FROM @sql;
--     EXECUTE stmt;
--     DEALLOCATE PREPARE stmt;
    
--     -- Update security context
--     UPDATE user_security_context 
--     SET is_active = FALSE 
--     WHERE user_name = p_username;
    
--     COMMIT;
--     FLUSH PRIVILEGES;
    
--     SET p_result_message = CONCAT('User ', p_username, ' deactivated successfully');
-- END //

-- -- Procedure to reset user password
-- CREATE PROCEDURE reset_user_password(
--     IN p_username VARCHAR(100),
--     IN p_new_password VARCHAR(100),
--     OUT p_result_message VARCHAR(255)
-- )
-- BEGIN
--     DECLARE v_sql_stmt TEXT;
--     DECLARE EXIT HANDLER FOR SQLEXCEPTION
--     BEGIN
--         ROLLBACK;
--         SET p_result_message = CONCAT('Error resetting password for user: ', p_username);
--     END;
    
--     START TRANSACTION;
    
--     -- Reset password
--     SET v_sql_stmt = CONCAT('ALTER USER ''', p_username, '''@''localhost'' IDENTIFIED BY ''', p_new_password, '''');
--     SET @sql = v_sql_stmt;
--     PREPARE stmt FROM @sql;
--     EXECUTE stmt;
--     DEALLOCATE PREPARE stmt;
    
--     -- Reset login attempts and unlock
--     UPDATE user_security_context 
--     SET login_attempts = 0, locked_until = NULL
--     WHERE user_name = p_username;
    
--     COMMIT;
--     FLUSH PRIVILEGES;
    
--     SET p_result_message = CONCAT('Password reset successfully for user: ', p_username);
-- END //

-- -- Procedure to unlock a user account
-- CREATE PROCEDURE unlock_user_account(
--     IN p_username VARCHAR(100),
--     OUT p_result_message VARCHAR(255)
-- )
-- BEGIN
--     UPDATE user_security_context 
--     SET login_attempts = 0, locked_until = NULL, is_active = TRUE
--     WHERE user_name = p_username;
    
--     IF ROW_COUNT() > 0 THEN
--         SET p_result_message = CONCAT('User account unlocked: ', p_username);
--     ELSE
--         SET p_result_message = CONCAT('User not found: ', p_username);
--     END IF;
-- END //

-- DELIMITER ;

-- -- =====================================================
-- -- 2. MONITORING AND REPORTING VIEWS
-- -- =====================================================

-- -- View for user activity monitoring
-- CREATE VIEW user_activity_report AS
-- SELECT 
--     usc.user_name,
--     usc.role_type,
--     usc.is_active,
--     usc.last_login,
--     usc.login_attempts,
--     usc.locked_until,
--     CASE 
--         WHEN usc.locked_until IS NOT NULL AND usc.locked_until > NOW() THEN 'LOCKED'
--         WHEN NOT usc.is_active THEN 'INACTIVE'
--         ELSE 'ACTIVE'
--     END AS account_status,
--     CONCAT(e.first_name, ' ', e.last_name) AS employee_name,
--     e.position AS employee_position
-- FROM user_security_context usc
-- LEFT JOIN employees e ON usc.employee_id = e.employee_id;

-- -- View for security audit summary
-- CREATE VIEW security_audit_summary AS
-- SELECT 
--     DATE(operation_time) AS audit_date,
--     table_name,
--     operation_type,
--     COUNT(*) AS operation_count,
--     COUNT(DISTINCT user_name) AS unique_users
-- FROM audit_log
-- GROUP BY DATE(operation_time), table_name, operation_type
-- ORDER BY audit_date DESC, operation_count DESC;

-- -- View for failed login attempts
-- CREATE VIEW failed_login_report AS
-- SELECT 
--     user_name,
--     role_type,
--     login_attempts,
--     locked_until,
--     last_login,
--     CASE 
--         WHEN login_attempts >= 3 THEN 'HIGH RISK'
--         WHEN login_attempts >= 2 THEN 'MEDIUM RISK'
--         WHEN login_attempts >= 1 THEN 'LOW RISK'
--         ELSE 'NO RISK'
--     END AS risk_level
-- FROM user_security_context
-- WHERE login_attempts > 0 OR locked_until IS NOT NULL
-- ORDER BY login_attempts DESC, locked_until DESC;

-- -- View for data access patterns
-- CREATE VIEW data_access_patterns AS
-- SELECT 
--     user_name,
--     table_name,
--     operation_type,
--     COUNT(*) AS access_count,
--     MIN(operation_time) AS first_access,
--     MAX(operation_time) AS last_access
-- FROM audit_log
-- WHERE operation_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
-- GROUP BY user_name, table_name, operation_type
-- ORDER BY access_count DESC;

-- -- =====================================================
-- -- 3. SECURITY MONITORING PROCEDURES
-- -- =====================================================

-- DELIMITER //

-- -- Procedure to generate security report
-- CREATE PROCEDURE generate_security_report()
-- BEGIN
--     -- User status summary
--     SELECT 'USER STATUS SUMMARY' AS report_section;
--     SELECT 
--         account_status,
--         COUNT(*) AS user_count
--     FROM user_activity_report
--     GROUP BY account_status;
    
--     -- Recent audit activity
--     SELECT 'RECENT AUDIT ACTIVITY (Last 7 days)' AS report_section;
--     SELECT 
--         audit_date,
--         table_name,
--         operation_type,
--         operation_count
--     FROM security_audit_summary
--     WHERE audit_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
--     ORDER BY audit_date DESC, operation_count DESC
--     LIMIT 20;
    
--     -- High-risk users
--     SELECT 'HIGH-RISK USERS' AS report_section;
--     SELECT 
--         user_name,
--         role_type,
--         login_attempts,
--         risk_level,
--         locked_until
--     FROM failed_login_report
--     WHERE risk_level IN ('HIGH RISK', 'MEDIUM RISK');
    
--     -- Top data accessors
--     SELECT 'TOP DATA ACCESSORS (Last 30 days)' AS report_section;
--     SELECT 
--         user_name,
--         SUM(access_count) AS total_operations
--     FROM data_access_patterns
--     GROUP BY user_name
--     ORDER BY total_operations DESC
--     LIMIT 10;
-- END //

-- -- Procedure to check for security violations
-- CREATE PROCEDURE check_security_violations()
-- BEGIN
--     DECLARE done INT DEFAULT FALSE;
--     DECLARE v_user_name VARCHAR(100);
--     DECLARE v_login_attempts INT;
--     DECLARE v_risk_count INT DEFAULT 0;
    
--     DECLARE risk_cursor CURSOR FOR
--         SELECT user_name, login_attempts
--         FROM user_security_context
--         WHERE login_attempts >= 2 AND is_active = TRUE;
    
--     DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
--     SELECT 'SECURITY VIOLATIONS CHECK' AS report_section;
    
--     -- Check for users with multiple failed login attempts
--     OPEN risk_cursor;
--     read_loop: LOOP
--         FETCH risk_cursor INTO v_user_name, v_login_attempts;
--         IF done THEN
--             LEAVE read_loop;
--         END IF;
        
--         SELECT 
--             v_user_name AS user_name,
--             v_login_attempts AS failed_attempts,
--             'Multiple failed login attempts' AS violation_type;
            
--         SET v_risk_count = v_risk_count + 1;
--     END LOOP;
--     CLOSE risk_cursor;
    
--     -- Check for unusual activity patterns
--     SELECT 
--         user_name,
--         table_name,
--         access_count,
--         'Unusual high access count' AS violation_type
--     FROM data_access_patterns
--     WHERE access_count > 100  -- Threshold for unusual activity
--     AND operation_time >= DATE_SUB(NOW(), INTERVAL 1 DAY);
    
--     -- Summary
--     SELECT 
--         v_risk_count AS users_with_failed_logins,
--         COUNT(*) AS users_with_high_activity
--     FROM data_access_patterns
--     WHERE access_count > 100
--     AND operation_time >= DATE_SUB(NOW(), INTERVAL 1 DAY);
-- END //

-- DELIMITER ;

-- -- =====================================================
-- -- 4. AUTOMATED SECURITY MAINTENANCE
-- -- =====================================================

-- -- Event to automatically unlock users after 30 minutes
-- -- CREATE EVENT auto_unlock_users
-- -- ON SCHEDULE EVERY 5 MINUTE
-- -- DO
-- --   UPDATE user_security_context 
-- --   SET locked_until = NULL, login_attempts = 0
-- --   WHERE locked_until IS NOT NULL AND locked_until <= NOW();

-- -- =====================================================
-- -- 5. GRANT PERMISSIONS FOR MANAGEMENT FUNCTIONS
-- -- =====================================================

-- -- Grant execute permissions on management procedures to admin and manager
-- GRANT EXECUTE ON PROCEDURE coffee_management_db.create_employee_user TO 'admin_role', 'manager_role';
-- GRANT EXECUTE ON PROCEDURE coffee_management_db.deactivate_user TO 'admin_role', 'manager_role';
-- GRANT EXECUTE ON PROCEDURE coffee_management_db.reset_user_password TO 'admin_role', 'manager_role';
-- GRANT EXECUTE ON PROCEDURE coffee_management_db.unlock_user_account TO 'admin_role', 'manager_role';
-- GRANT EXECUTE ON PROCEDURE coffee_management_db.generate_security_report TO 'admin_role', 'manager_role';
-- GRANT EXECUTE ON PROCEDURE coffee_management_db.check_security_violations TO 'admin_role', 'manager_role';

-- -- Grant select permissions on monitoring views
-- GRANT SELECT ON coffee_management_db.user_activity_report TO 'admin_role', 'manager_role';
-- GRANT SELECT ON coffee_management_db.security_audit_summary TO 'admin_role', 'manager_role';
-- GRANT SELECT ON coffee_management_db.failed_login_report TO 'admin_role', 'manager_role';
-- GRANT SELECT ON coffee_management_db.data_access_patterns TO 'admin_role', 'manager_role';

-- -- =====================================================
-- -- 6. SAMPLE USAGE COMMANDS
-- -- =====================================================

-- -- Examples of how to use the management procedures:

-- -- Create a new cashier user
-- -- CALL create_employee_user('cashier4', 'NewPassword123!', 5, 'Cashier', @result);
-- -- SELECT @result;

-- -- Reset a user's password
-- -- CALL reset_user_password('cashier1', 'NewSecurePass456!', @result);
-- -- SELECT @result;

-- -- Unlock a user account
-- -- CALL unlock_user_account('cashier2', @result);
-- -- SELECT @result;

-- -- Generate security report
-- -- CALL generate_security_report();

-- -- Check for security violations
-- -- CALL check_security_violations();

-- -- =====================================================
-- -- 7. FINAL SETUP
-- -- =====================================================

-- FLUSH PRIVILEGES;

-- SELECT 
--     'ACCESS CONTROL MANAGEMENT SYSTEM READY' AS status,
--     'Use the provided procedures to manage users and monitor security' AS message;
