import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { connectToAivenDB } from './src/config/ConnectSQlWithAiven.js'; 

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(morgan('dev'));
app.use(helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Import and use routes one by one to identify the problematic one
console.log('Loading customer routes...');
try {
    const customerRoutes = await import('./src/routes/customerRoutes.js');
    app.use('/api/customers', customerRoutes.default);
    console.log('✅ Customer routes loaded');
} catch (error) {
    console.error('❌ Customer routes failed:', error.message);
}

console.log('Loading employee routes...');
try {
    const employeeRoutes = await import('./src/routes/employeeRoutes.js');
    app.use('/api/employees', employeeRoutes.default);
    console.log('✅ Employee routes loaded');
} catch (error) {
    console.error('❌ Employee routes failed:', error.message);
}

console.log('Loading supplier routes...');
try {
    const supplierRoutes = await import('./src/routes/supplierRoutes.js');
    app.use('/api/suppliers', supplierRoutes.default);
    console.log('✅ Supplier routes loaded');
} catch (error) {
    console.error('❌ Supplier routes failed:', error.message);
}

console.log('Loading material routes...');
try {
    const materialRoutes = await import('./src/routes/materialRoutes.js');
    app.use('/api/materials', materialRoutes.default);
    console.log('✅ Material routes loaded');
} catch (error) {
    console.error('❌ Material routes failed:', error.message);
}

console.log('Loading product routes...');
try {
    const productRoutes = await import('./src/routes/productRoutes.js');
    app.use('/api/products', productRoutes.default);
    console.log('✅ Product routes loaded');
} catch (error) {
    console.error('❌ Product routes failed:', error.message);
}

console.log('Loading order routes...');
try {
    const orderRoutes = await import('./src/routes/orderRoutes.js');
    app.use('/api/orders', orderRoutes.default);
    console.log('✅ Order routes loaded');
} catch (error) {
    console.error('❌ Order routes failed:', error.message);
}

console.log('Loading payment routes...');
try {
    const paymentRoutes = await import('./src/routes/paymentRoutes.js');
    app.use('/api/payments', paymentRoutes.default);
    console.log('✅ Payment routes loaded');
} catch (error) {
    console.error('❌ Payment routes failed:', error.message);
}

console.log('Loading analytics routes...');
try {
    const analyticsRoutes = await import('./src/routes/analyticsRoutes.js');
    app.use('/api/analytics', analyticsRoutes.default);
    console.log('✅ Analytics routes loaded');
} catch (error) {
    console.error('❌ Analytics routes failed:', error.message);
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({ 
        status: 'OK', 
        message: 'Coffee Management System API is running',
        timestamp: new Date().toISOString()
    });
});

// Root endpoint
app.get('/', (req, res) => {
    res.json({
        message: 'Welcome to Coffee Management System API',
        version: '1.0.0',
        endpoints: {
            customers: '/api/customers',
            employees: '/api/employees',
            suppliers: '/api/suppliers',
            materials: '/api/materials',
            products: '/api/products',
            orders: '/api/orders',
            payments: '/api/payments',
            analytics: '/api/analytics',
            health: '/health'
        }
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(err.status || 500).json({
        error: err.message || 'Internal Server Error',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`
    });
});

// Test Aiven database connection on startup
async function testAivenConnection() {
    try {
        const connection = await connectToAivenDB();
        console.log('✅ Connected to Aiven MySQL database successfully!');
        
        // Test query
        const [rows] = await connection.execute('SELECT 1 + 1 AS solution');
        console.log('🔍 Test query result:', rows);
        
        connection.release();
        return true;
    } catch (error) {
        console.error('❌ Aiven database connection failed:', error.message);
        return false;
    }
}

// Start server
app.listen(PORT, async () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   GET  /                    - API information`);
    console.log(`   GET  /health              - Health check`);
    console.log(`   *    /api/customers       - Customer management`);
    console.log(`   *    /api/employees       - Employee management`);
    console.log(`   *    /api/suppliers       - Supplier management`);
    console.log(`   *    /api/materials       - Material management`);
    console.log(`   *    /api/products        - Product management`);
    console.log(`   *    /api/orders          - Order management`);
    console.log(`   *    /api/payments        - Payment management`);
    console.log(`   *    /api/analytics       - Analytics and reports`);
    
    // Test database connection
    const dbConnected = await testAivenConnection();
    if (!dbConnected) {
        console.log('⚠️  Server started but database connection failed');
        console.log('   Please check your .env file and database configuration');
    }
});
