import express from 'express';
import PaymentController from '../controllers/paymentController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Payment:
 *       type: object
 *       required:
 *         - employee_id
 *         - order_id
 *         - amount_money
 *       properties:
 *         payment_id:
 *           type: integer
 *           description: Auto-generated payment ID
 *         employee_id:
 *           type: integer
 *           description: Reference to employee who processed payment
 *         order_id:
 *           type: integer
 *           description: Reference to order (unique)
 *         payment_date:
 *           type: string
 *           format: date-time
 *           description: Payment timestamp
 *         payment_type:
 *           type: string
 *           enum: [ABA, Aceleda, Cash, Other]
 *           default: Other
 *           description: Payment method
 *         amount_money:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *           description: Payment amount
 *         employee_name:
 *           type: string
 *           description: Employee name (read-only)
 *         customer_name:
 *           type: string
 *           nullable: true
 *           description: Customer name (read-only)
 *         product_name:
 *           type: string
 *           description: Product name (read-only)
 *       example:
 *         payment_id: 1
 *         employee_id: 1
 *         order_id: 1
 *         payment_date: "2024-01-15T10:30:00Z"
 *         payment_type: "Cash"
 *         amount_money: 9.00
 *         employee_name: "<PERSON>"
 *         customer_name: "<PERSON>"
 *         product_name: "<PERSON><PERSON><PERSON><PERSON>"
 *
 *     PaymentInput:
 *       type: object
 *       required:
 *         - employee_id
 *         - order_id
 *         - amount_money
 *       properties:
 *         employee_id:
 *           type: integer
 *         order_id:
 *           type: integer
 *         payment_type:
 *           type: string
 *           enum: [ABA, Aceleda, Cash, Other]
 *           default: Other
 *         amount_money:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *       example:
 *         employee_id: 1
 *         order_id: 1
 *         payment_type: "Cash"
 *         amount_money: 9.00
 */

/**
 * @swagger
 * /api/payments:
 *   get:
 *     summary: Get all payments with pagination and filtering
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of payments per page
 *       - in: query
 *         name: employee_id
 *         schema:
 *           type: integer
 *         description: Filter by employee ID
 *       - in: query
 *         name: payment_type
 *         schema:
 *           type: string
 *           enum: [ABA, Aceleda, Cash, Other]
 *         description: Filter by payment type
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter payments from this date
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter payments to this date
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Payment'
 *                 pagination:
 *                   type: object
 */
router.get('/', PaymentController.getAllPayments);

/**
 * @swagger
 * /api/payments/{id}:
 *   get:
 *     summary: Get payment by ID
 *     tags: [Payments]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Payment ID
 *     responses:
 *       200:
 *         description: Payment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *       404:
 *         description: Payment not found
 */
router.get('/:id', PaymentController.getPaymentById);

/**
 * @swagger
 * /api/payments:
 *   post:
 *     summary: Create a new payment (automatically creates loyalty points)
 *     tags: [Payments]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PaymentInput'
 *     responses:
 *       201:
 *         description: Payment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Payment'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Employee or order not found
 *       409:
 *         description: Order already has a payment
 */
router.post('/', PaymentController.createPayment);

/**
 * @swagger
 * /api/payments/summary:
 *   get:
 *     summary: Get payment summary by date range
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for summary
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for summary
 *     responses:
 *       200:
 *         description: Payment summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       payment_date:
 *                         type: string
 *                         format: date
 *                       payment_type:
 *                         type: string
 *                       transaction_count:
 *                         type: integer
 *                       total_amount:
 *                         type: number
 */
router.get('/summary', PaymentController.getPaymentSummary);

/**
 * @swagger
 * /api/payments/revenue/daily:
 *   get:
 *     summary: Get daily revenue
 *     tags: [Payments]
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: Number of days to retrieve revenue for
 *     responses:
 *       200:
 *         description: Daily revenue retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       total_revenue:
 *                         type: number
 *                       transaction_count:
 *                         type: integer
 */
router.get('/revenue/daily', PaymentController.getDailyRevenue);

export default router;
