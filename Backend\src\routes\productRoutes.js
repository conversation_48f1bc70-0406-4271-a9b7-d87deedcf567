import express from 'express';
import ProductController from '../controllers/productController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - product_name
 *         - category_name
 *         - unit_price
 *       properties:
 *         product_id:
 *           type: integer
 *           description: Auto-generated product ID
 *         product_name:
 *           type: string
 *           maxLength: 100
 *           description: Product name
 *         material_eating_id:
 *           type: integer
 *           nullable: true
 *           description: Reference to material eating
 *         material_object_id:
 *           type: integer
 *           nullable: true
 *           description: Reference to material object
 *         category_name:
 *           type: string
 *           enum: [Coffee, Tea, Pastry, Sandwich, Juice, Other]
 *           description: Product category
 *         description:
 *           type: string
 *           nullable: true
 *           description: Product description
 *         unit_price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *           description: Product unit price
 *         eating_material:
 *           type: string
 *           nullable: true
 *           description: Associated eating material name (read-only)
 *         object_material:
 *           type: string
 *           nullable: true
 *           description: Associated object material name (read-only)
 *       example:
 *         product_id: 1
 *         product_name: "Cappuccino"
 *         material_eating_id: 1
 *         material_object_id: 2
 *         category_name: "Coffee"
 *         description: "Rich and creamy cappuccino"
 *         unit_price: 4.50
 *         eating_material: "Coffee Bean"
 *         object_material: "Paper Cup"
 *
 *     ProductInput:
 *       type: object
 *       required:
 *         - product_name
 *         - category_name
 *         - unit_price
 *       properties:
 *         product_name:
 *           type: string
 *           maxLength: 100
 *         material_eating_id:
 *           type: integer
 *           nullable: true
 *         material_object_id:
 *           type: integer
 *           nullable: true
 *         category_name:
 *           type: string
 *           enum: [Coffee, Tea, Pastry, Sandwich, Juice, Other]
 *         description:
 *           type: string
 *           nullable: true
 *         unit_price:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *       example:
 *         product_name: "Cappuccino"
 *         material_eating_id: 1
 *         material_object_id: 2
 *         category_name: "Coffee"
 *         description: "Rich and creamy cappuccino"
 *         unit_price: 4.50
 */

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get all products with pagination and filtering
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of products per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by product name or description
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [Coffee, Tea, Pastry, Sandwich, Juice, Other]
 *         description: Filter by category
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *                 pagination:
 *                   type: object
 */
router.get('/', ProductController.getAllProducts);

/**
 * @swagger
 * /api/products/{id}:
 *   get:
 *     summary: Get product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       404:
 *         description: Product not found
 */
router.get('/:id', ProductController.getProductById);

/**
 * @swagger
 * /api/products:
 *   post:
 *     summary: Create a new product
 *     tags: [Products]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ProductInput'
 *     responses:
 *       201:
 *         description: Product created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Product'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Material not found
 */
router.post('/', ProductController.createProduct);

/**
 * @swagger
 * /api/products/{id}:
 *   put:
 *     summary: Update product
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ProductInput'
 *     responses:
 *       200:
 *         description: Product updated successfully
 *       404:
 *         description: Product not found
 */
router.put('/:id', ProductController.updateProduct);

/**
 * @swagger
 * /api/products/{id}:
 *   delete:
 *     summary: Delete product
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product deleted successfully
 *       404:
 *         description: Product not found
 *       409:
 *         description: Cannot delete product with existing orders
 */
router.delete('/:id', ProductController.deleteProduct);

/**
 * @swagger
 * /api/products/category/{category}:
 *   get:
 *     summary: Get products by category
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [Coffee, Tea, Pastry, Sandwich, Juice, Other]
 *         description: Product category
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Product'
 *       400:
 *         description: Invalid category
 */
router.get('/category/:category', ProductController.getProductsByCategory);

export default router;
