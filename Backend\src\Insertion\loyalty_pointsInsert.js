import { connectToAivenDB } from '../config/ConnectSQlWithAiven.js';
import { faker } from '@faker-js/faker';

async function insertLoyaltyPoints() {
  let connection;
  try {
    connection = await connectToAivenDB();
    // Get order IDs for foreign keys
    const [orders] = await connection.query('SELECT order_id FROM orders LIMIT 800000');
    if (orders.length === 0) {
      throw new Error('Orders table is empty. Please insert data first.');
    }
    const batchSize = 10000; // Insert in batches to avoid memory/timeout issues
    let totalInserted = 0;
    for (let batch = 0; batch < 80; batch++) {
      const points = [];
      for (let i = 0; i < batchSize; i++) {
        const orderObj = orders[batch * batchSize + i];
        if (!orderObj) break;
        const orderId = orderObj.order_id;
        const pointsEarned = faker.number.int({ min: 1, max: 100 });
        points.push([
          orderId,
          pointsEarned
        ]);
      }
      const sql = `INSERT INTO loyalty_points (order_id, points_earned) VALUES ?`;
      await connection.query(sql, [points]);
      totalInserted += points.length;
      console.log(`Inserted ${totalInserted} loyalty_points so far...`);
    }
    console.log('Inserted 800,000 loyalty_points successfully!');
  } catch (err) {
    console.log('Error', err);
  } finally {
    if (connection) await connection.end();
  }
}

insertLoyaltyPoints();
