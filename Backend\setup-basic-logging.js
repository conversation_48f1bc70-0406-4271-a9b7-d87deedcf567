import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database configuration
const dbConfig = {
    host: process.env.DBP_HOST || 'localhost',
    user: process.env.DBP_USER || 'root',
    password: process.env.DBP_PASSWORD || '12345',
    database: process.env.DBP_NAME || 'coffee_management_db',
    port: parseInt(process.env.DBP_PORT) || 3306
};

async function setupBasicLogging() {
    let connection;
    
    try {
        console.log('🔗 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Connected to database successfully!');
        
        // Read the SQL file
        const sqlFilePath = path.join(__dirname, 'setup-basic-logging.sql');
        const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
        
        console.log('📊 Creating basic activity logging tables...');
        
        // Split by semicolon and execute each statement
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0);
        
        for (const statement of statements) {
            if (statement.toLowerCase().includes('create') || 
                statement.toLowerCase().includes('insert') ||
                statement.toLowerCase().includes('select')) {
                try {
                    await connection.execute(statement);
                    console.log('✅ Executed:', statement.substring(0, 50) + '...');
                } catch (error) {
                    console.log(`⚠️ Warning: ${error.message}`);
                }
            }
        }
        
        console.log('\n🎉 Basic activity logging setup completed!');
        
    } catch (error) {
        console.error('❌ Error setting up basic logging:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔒 Database connection closed.');
        }
    }
}

setupBasicLogging();
