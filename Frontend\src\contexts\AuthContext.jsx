import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // API base URL
    const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

    // Check for existing token on app load and validate it
    useEffect(() => {
        const validateToken = async () => {
            const token = localStorage.getItem('auth_token');
            
            if (token) {
                try {
                    const response = await fetch(`${API_BASE_URL}/auth/validate`, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            setUser(data.data.user);
                        } else {
                            // Token invalid, remove it
                            localStorage.removeItem('auth_token');
                            localStorage.removeItem('user_data');
                        }
                    } else {
                        // Token invalid, remove it
                        localStorage.removeItem('auth_token');
                        localStorage.removeItem('user_data');
                    }
                } catch (err) {
                    console.error('Token validation failed:', err);
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_data');
                }
            }
            setLoading(false);
        };

        validateToken();
    }, [API_BASE_URL]);

    const login = async (credentials) => {
        try {
            setLoading(true);
            setError(null);
            
            // For demo purposes, we'll use mock authentication
            // Replace this with actual API call when backend auth is ready
            const mockResponse = {
                success: true,
                data: {
                    user: {
                        username: credentials.username,
                        role: 'admin',
                        firstName: 'Admin',
                        lastName: 'User',
                        email: '<EMAIL>'
                    },
                    token: 'mock-jwt-token-' + Date.now()
                }
            };

            if (credentials.username === 'admin' && credentials.password === 'admin123') {
                const { user: userData, token } = mockResponse.data;
                
                // Store in localStorage
                localStorage.setItem('auth_token', token);
                localStorage.setItem('user_data', JSON.stringify(userData));
                setUser(userData);
                return { success: true, data: userData };
            } else {
                throw new Error('Invalid credentials');
            }
        } catch (err) {
            setError(err.message || 'Login failed');
            return { success: false, error: err.message };
        } finally {
            setLoading(false);
        }
    };

    const logout = async () => {
        try {
            const token = localStorage.getItem('auth_token');
            
            if (token) {
                // Call logout endpoint (optional, since JWT is stateless)
                await fetch(`${API_BASE_URL}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    },
                });
            }
        } catch (err) {
            console.error('Logout API call failed:', err);
        } finally {
            // Always clean up local storage
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            setUser(null);
            setError(null);
        }
    };

    const changePassword = async (currentPassword, newPassword) => {
        try {
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`${API_BASE_URL}/auth/change-password`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    currentPassword,
                    newPassword
                }),
            });

            const data = await response.json();

            if (response.ok && data.success) {
                return { success: true, message: data.message };
            } else {
                return { success: false, error: data.message || 'Password change failed' };
            }
        } catch (err) {
            return { success: false, error: err.message || 'Network error occurred' };
        }
    };

    const getProfile = async () => {
        try {
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                throw new Error('No authentication token found');
            }

            const response = await fetch(`${API_BASE_URL}/auth/profile`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            const data = await response.json();

            if (response.ok && data.success) {
                return { success: true, data: data.data };
            } else {
                return { success: false, error: data.message || 'Failed to fetch profile' };
            }
        } catch (err) {
            return { success: false, error: err.message || 'Network error occurred' };
        }
    };

    // Helper function to get auth headers for API calls
    const getAuthHeaders = () => {
        const token = localStorage.getItem('auth_token');
        return token ? {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        } : {
            'Content-Type': 'application/json',
        };
    };

    const value = {
        user,
        login,
        logout,
        changePassword,
        getProfile,
        getAuthHeaders,
        loading,
        error,
        isAuthenticated: !!user
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
