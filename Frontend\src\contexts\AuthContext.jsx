import React, { createContext, useContext, useState } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState({ username: 'admin', role: 'admin' });
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    // API base URL
    const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

    // Since backend doesn't have auth set up, we'll use a mock authenticated state
    const isAuthenticated = true;

    const login = async (credentials) => {
        try {
            setLoading(true);
            setError(null);
            
            // For demo purposes, we'll use mock authentication
            // Replace this with actual API call when backend auth is ready
            const mockResponse = {
                success: true,
                data: {
                    user: {
                        username: credentials.username,
                        role: 'admin',
                        firstName: 'Admin',
                        lastName: 'User',
                        email: '<EMAIL>'
                    },
                    token: 'mock-jwt-token-' + Date.now()
                }
            };

            if (credentials.username === 'admin' && credentials.password === 'admin123') {
                const { user: userData, token } = mockResponse.data;
                
                // Store in localStorage
                localStorage.setItem('auth_token', token);
                localStorage.setItem('user_data', JSON.stringify(userData));
                setUser(userData);
                return { success: true, data: userData };
            } else {
                throw new Error('Invalid credentials');
            }
        } catch (err) {
            setError(err.message || 'Login failed');
            return { success: false, error: err.message };
        } finally {
            setLoading(false);
        }
    };

    const logout = async () => {
        try {
            // Clean up local storage
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            setUser(null);
            setError(null);
        } catch (err) {
            console.error('Logout failed:', err);
        }
    };

    const changePassword = async (currentPassword, newPassword) => {
        // Mock function for now
        return { success: true, message: 'Password changed successfully' };
    };

    const getProfile = async () => {
        // Mock function for now
        return { success: true, data: user };
    };

    // Helper function to get auth headers for API calls
    const getAuthHeaders = () => {
        const token = localStorage.getItem('auth_token');
        return token ? {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        } : {
            'Content-Type': 'application/json',
        };
    };

    const value = {
        user,
        login,
        logout,
        changePassword,
        getProfile,
        getAuthHeaders,
        loading,
        error,
        isAuthenticated
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
