import React, { createContext, useContext, useState } from 'react';
import { coffeeAPI } from '../components/services/api';

const AuthContext = createContext();

export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

export const AuthProvider = ({ children }) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // API base URL
    const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api';

    // Check if user is authenticated
    const isAuthenticated = !!user;

    // Initialize auth state from localStorage
    React.useEffect(() => {
        const initAuth = async () => {
            const token = localStorage.getItem('auth_token');
            const userData = localStorage.getItem('user_data');

            if (token && userData) {
                try {
                    const parsedUser = JSON.parse(userData);
                    // Verify token with backend
                    const response = await fetch(`${API_BASE_URL}/auth/me`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        setUser(parsedUser);
                    } else {
                        // Token is invalid, clear storage
                        localStorage.removeItem('auth_token');
                        localStorage.removeItem('user_data');
                    }
                } catch (err) {
                    console.error('Auth initialization error:', err);
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_data');
                }
            }
            setLoading(false);
        };

        initAuth();
    }, []);

    const login = async (credentials) => {
        try {
            setLoading(true);
            setError(null);

            // Call the backend API for authentication
            const response = await fetch(`${API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: credentials.username,
                    password: credentials.password
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Login failed');
            }

            if (data.success) {
                const userData = {
                    username: data.data.username,
                    role: data.data.role,
                    name: data.data.name
                };

                // Store in localStorage
                localStorage.setItem('auth_token', data.data.token);
                localStorage.setItem('user_data', JSON.stringify(userData));
                setUser(userData);
                return { success: true, data: userData };
            } else {
                throw new Error(data.message || 'Login failed');
            }
        } catch (err) {
            setError(err.message || 'Login failed');
            return { success: false, error: err.message };
        } finally {
            setLoading(false);
        }
    };

    const logout = async () => {
        try {
            const token = localStorage.getItem('auth_token');

            // Call backend logout endpoint if token exists
            if (token) {
                try {
                    await fetch(`${API_BASE_URL}/auth/logout`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });
                } catch (err) {
                    console.error('Backend logout failed:', err);
                }
            }

            // Clean up local storage
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
            setUser(null);
            setError(null);
        } catch (err) {
            console.error('Logout failed:', err);
        }
    };

    const changePassword = async (currentPassword, newPassword) => {
        // Mock function for now
        return { success: true, message: 'Password changed successfully' };
    };

    const getProfile = async () => {
        // Mock function for now
        return { success: true, data: user };
    };

    // Helper function to get auth headers for API calls
    const getAuthHeaders = () => {
        const token = localStorage.getItem('auth_token');
        return token ? {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
        } : {
            'Content-Type': 'application/json',
        };
    };

    const value = {
        user,
        login,
        logout,
        changePassword,
        getProfile,
        getAuthHeaders,
        loading,
        error,
        isAuthenticated,
        API_BASE_URL
    };

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    );
};
