import AuthService from '../services/authService.js';

/**
 * Authentication Controller
 * Handles user authentication and management
 */
class AuthController {
    /**
     * Login user
     */
    static async login(req, res) {
        try {
            const { username, password } = req.body;

            if (!username || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'Username and password are required'
                });
            }

            const user = AuthService.authenticate(username, password);
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid username or password'
                });
            }

            res.json({
                success: true,
                message: 'Login successful',
                data: user
            });
        } catch (error) {
            console.error('Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Login failed',
                error: error.message
            });
        }
    }

    /**
     * Logout user
     */
    static async logout(req, res) {
        try {
            const authHeader = req.headers['authorization'];
            const token = authHeader && authHeader.split(' ')[1];

            if (token) {
                AuthService.logout(token);
            }

            res.json({
                success: true,
                message: 'Logout successful'
            });
        } catch (error) {
            console.error('Logout error:', error);
            res.status(500).json({
                success: false,
                message: 'Logout failed',
                error: error.message
            });
        }
    }

    /**
     * Get current user info
     */
    static async getCurrentUser(req, res) {
        try {
            res.json({
                success: true,
                message: 'User info retrieved',
                data: {
                    username: req.user.username,
                    role: req.user.role,
                    name: req.user.name
                }
            });
        } catch (error) {
            console.error('Get current user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve user info',
                error: error.message
            });
        }
    }

    /**
     * Get all users (admin only)
     */
    static async getAllUsers(req, res) {
        try {
            const users = AuthService.getUsers().map(user => ({
                username: user.username,
                role: user.role,
                name: user.name
            }));

            res.json({
                success: true,
                message: 'Users retrieved successfully',
                data: users
            });
        } catch (error) {
            console.error('Get all users error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve users',
                error: error.message
            });
        }
    }

    /**
     * Create new user (admin only)
     */
    static async createUser(req, res) {
        try {
            const { username, password, role } = req.body;

            if (!username || !password) {
                return res.status(400).json({
                    success: false,
                    message: 'Username and password are required'
                });
            }

            // Validate role
            const validRoles = ['admin', 'manager', 'cashier', 'user'];
            if (role && !validRoles.includes(role)) {
                return res.status(400).json({
                    success: false,
                    message: `Role must be one of: ${validRoles.join(', ')}`
                });
            }

            const success = AuthService.createUser({
                username,
                password,
                role: role || 'user'
            });

            if (!success) {
                return res.status(409).json({
                    success: false,
                    message: 'Username already exists'
                });
            }

            res.status(201).json({
                success: true,
                message: 'User created successfully',
                data: {
                    username,
                    role: role || 'user'
                }
            });
        } catch (error) {
            console.error('Create user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create user',
                error: error.message
            });
        }
    }

    /**
     * Update user (admin only)
     */
    static async updateUser(req, res) {
        try {
            const { username } = req.params;
            const { password, role } = req.body;

            if (!password && !role) {
                return res.status(400).json({
                    success: false,
                    message: 'At least one field to update is required'
                });
            }

            // Validate role
            const validRoles = ['admin', 'manager', 'cashier', 'user'];
            if (role && !validRoles.includes(role)) {
                return res.status(400).json({
                    success: false,
                    message: `Role must be one of: ${validRoles.join(', ')}`
                });
            }

            const success = AuthService.updateUser(username, {
                password,
                role
            });

            if (!success) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            res.json({
                success: true,
                message: 'User updated successfully',
                data: {
                    username,
                    role: role || 'user'
                }
            });
        } catch (error) {
            console.error('Update user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update user',
                error: error.message
            });
        }
    }

    /**
     * Delete user (admin only)
     */
    static async deleteUser(req, res) {
        try {
            const { username } = req.params;

            const success = AuthService.deleteUser(username);

            if (!success) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found or cannot delete admin'
                });
            }

            res.json({
                success: true,
                message: 'User deleted successfully'
            });
        } catch (error) {
            console.error('Delete user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete user',
                error: error.message
            });
        }
    }
}

export default AuthController;
