import DatabaseService from '../services/databaseService.js';

/**
 * Supplier Controller
 * Handles all supplier-related operations
 */

class SupplierController {
    /**
     * Get all suppliers with pagination and filtering
     */
    static async getAllSuppliers(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search || '';
            const category = req.query.category || '';

            let baseQuery = `
                SELECT 
                    supplier_id,
                    supplies_name,
                    contact_email,
                    phone_number,
                    location_url,
                    category
                FROM suppliers
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (search) {
                whereConditions.push(`(
                    supplies_name LIKE ? OR 
                    contact_email LIKE ?
                )`);
                const searchParam = `%${search}%`;
                params.push(searchParam, searchParam);
            }
            
            if (category) {
                whereConditions.push('category = ?');
                params.push(category);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY supplies_name ASC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Suppliers retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting suppliers:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve suppliers',
                error: error.message
            });
        }
    }

    /**
     * Get supplier by ID
     */
    static async getSupplierById(req, res) {
        try {
            const { id } = req.params;
            
            const supplier = await DatabaseService.executeQuerySingle(
                `SELECT 
                    supplier_id,
                    supplies_name,
                    contact_email,
                    phone_number,
                    location_url,
                    category
                FROM suppliers 
                WHERE supplier_id = ?`,
                [id]
            );

            if (!supplier) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found'
                });
            }

            res.json({
                success: true,
                message: 'Supplier retrieved successfully',
                data: supplier
            });
        } catch (error) {
            console.error('Error getting supplier:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve supplier',
                error: error.message
            });
        }
    }

    /**
     * Create new supplier
     */
    static async createSupplier(req, res) {
        try {
            const { 
                supplies_name, 
                contact_email, 
                phone_number, 
                location_url, 
                category 
            } = req.body;

            // Validation
            if (!supplies_name || !contact_email || !phone_number || !location_url) {
                return res.status(400).json({
                    success: false,
                    message: 'Required fields: supplies_name, contact_email, phone_number, location_url'
                });
            }

            // Validate category enum
            const validCategories = ['eating material', 'material object'];
            const supplierCategory = category || 'material object';
            
            if (!validCategories.includes(supplierCategory)) {
                return res.status(400).json({
                    success: false,
                    message: `Category must be one of: ${validCategories.join(', ')}`
                });
            }

            const supplierId = await DatabaseService.executeInsert(
                `INSERT INTO suppliers (supplies_name, contact_email, phone_number, location_url, category) 
                 VALUES (?, ?, ?, ?, ?)`,
                [supplies_name, contact_email, phone_number, location_url, supplierCategory]
            );

            const newSupplier = await DatabaseService.executeQuerySingle(
                `SELECT 
                    supplier_id,
                    supplies_name,
                    contact_email,
                    phone_number,
                    location_url,
                    category
                FROM suppliers 
                WHERE supplier_id = ?`,
                [supplierId]
            );

            res.status(201).json({
                success: true,
                message: 'Supplier created successfully',
                data: newSupplier
            });
        } catch (error) {
            console.error('Error creating supplier:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create supplier',
                error: error.message
            });
        }
    }

    /**
     * Update supplier
     */
    static async updateSupplier(req, res) {
        try {
            const { id } = req.params;
            const { 
                supplies_name, 
                contact_email, 
                phone_number, 
                location_url, 
                category 
            } = req.body;

            // Check if supplier exists
            const existingSupplier = await DatabaseService.recordExists('suppliers', 'supplier_id', id);
            if (!existingSupplier) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found'
                });
            }

            // Validate category enum if provided
            if (category) {
                const validCategories = ['eating material', 'material object'];
                if (!validCategories.includes(category)) {
                    return res.status(400).json({
                        success: false,
                        message: `Category must be one of: ${validCategories.join(', ')}`
                    });
                }
            }

            const affectedRows = await DatabaseService.executeUpdate(
                `UPDATE suppliers 
                 SET supplies_name = ?, contact_email = ?, phone_number = ?, 
                     location_url = ?, category = ?
                 WHERE supplier_id = ?`,
                [supplies_name, contact_email, phone_number, location_url, category, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found or no changes made'
                });
            }

            const updatedSupplier = await DatabaseService.executeQuerySingle(
                `SELECT 
                    supplier_id,
                    supplies_name,
                    contact_email,
                    phone_number,
                    location_url,
                    category
                FROM suppliers 
                WHERE supplier_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Supplier updated successfully',
                data: updatedSupplier
            });
        } catch (error) {
            console.error('Error updating supplier:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update supplier',
                error: error.message
            });
        }
    }

    /**
     * Delete supplier
     */
    static async deleteSupplier(req, res) {
        try {
            const { id } = req.params;

            // Check if supplier has related records
            const hasMaterialEating = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM material_eating WHERE supplier_id = ? LIMIT 1',
                [id]
            );

            const hasMaterialObject = await DatabaseService.executeQuerySingle(
                'SELECT 1 FROM material_object WHERE supplier_id = ? LIMIT 1',
                [id]
            );

            if (hasMaterialEating || hasMaterialObject) {
                return res.status(409).json({
                    success: false,
                    message: 'Cannot delete supplier with existing material records'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                'DELETE FROM suppliers WHERE supplier_id = ?',
                [id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found'
                });
            }

            res.json({
                success: true,
                message: 'Supplier deleted successfully'
            });
        } catch (error) {
            console.error('Error deleting supplier:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete supplier',
                error: error.message
            });
        }
    }

    /**
     * Get suppliers by category
     */
    static async getSuppliersByCategory(req, res) {
        try {
            const { category } = req.params;
            
            const validCategories = ['eating material', 'material object'];
            if (!validCategories.includes(category)) {
                return res.status(400).json({
                    success: false,
                    message: `Category must be one of: ${validCategories.join(', ')}`
                });
            }

            const suppliers = await DatabaseService.executeQuery(
                `SELECT 
                    supplier_id,
                    supplies_name,
                    contact_email,
                    phone_number,
                    location_url,
                    category
                FROM suppliers 
                WHERE category = ?
                ORDER BY supplies_name ASC`,
                [category]
            );

            res.json({
                success: true,
                message: `${category} suppliers retrieved successfully`,
                data: suppliers
            });
        } catch (error) {
            console.error('Error getting suppliers by category:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve suppliers by category',
                error: error.message
            });
        }
    }

    /**
     * Get supplier spending summary
     */
    static async getSupplierSpending(req, res) {
        try {
            const { id } = req.params;

            // Check if supplier exists
            const supplierExists = await DatabaseService.recordExists('suppliers', 'supplier_id', id);
            if (!supplierExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found'
                });
            }

            const spending = await DatabaseService.executeQuerySingle(
                `SELECT 
                    s.supplies_name,
                    s.category,
                    COALESCE(SUM(me.unit_price * me.quantity), 0) + 
                    COALESCE(SUM(mo.unit_price * mo.quantity), 0) AS total_spent
                FROM suppliers s
                LEFT JOIN material_eating me ON s.supplier_id = me.supplier_id
                LEFT JOIN material_object mo ON s.supplier_id = mo.supplier_id
                WHERE s.supplier_id = ?
                GROUP BY s.supplier_id`,
                [id]
            );

            res.json({
                success: true,
                message: 'Supplier spending retrieved successfully',
                data: spending
            });
        } catch (error) {
            console.error('Error getting supplier spending:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve supplier spending',
                error: error.message
            });
        }
    }
}

export default SupplierController;
