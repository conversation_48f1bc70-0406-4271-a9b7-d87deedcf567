import express from 'express';
import EmployeeController from '../controllers/employeeController.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     Employee:
 *       type: object
 *       required:
 *         - first_name
 *         - last_name
 *         - position
 *         - hire_date
 *         - salary
 *         - phone_number
 *         - email
 *         - address
 *       properties:
 *         employee_id:
 *           type: integer
 *           description: Auto-generated employee ID
 *         first_name:
 *           type: string
 *           maxLength: 50
 *           description: <PERSON><PERSON>loyee's first name
 *         last_name:
 *           type: string
 *           maxLength: 50
 *           description: Em<PERSON>loyee's last name
 *         position:
 *           type: string
 *           enum: [Manager, Cashier, Vendor, <PERSON><PERSON>]
 *           description: Employee's position
 *         hire_date:
 *           type: string
 *           format: date
 *           description: Em<PERSON>loyee's hire date
 *         salary:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *           description: Em<PERSON>loyee's salary
 *         phone_number:
 *           type: string
 *           maxLength: 20
 *           description: Employee's phone number
 *         email:
 *           type: string
 *           maxLength: 100
 *           format: email
 *           description: Employee's email address (unique)
 *         address:
 *           type: string
 *           maxLength: 255
 *           description: Employee's address
 *       example:
 *         employee_id: 1
 *         first_name: "<PERSON>"
 *         last_name: "<PERSON>"
 *         position: "Manager"
 *         hire_date: "2024-01-15"
 *         salary: 5000.00
 *         phone_number: "+1234567890"
 *         email: "<EMAIL>"
 *         address: "123 Main St, City, State"
 *
 *     EmployeeInput:
 *       type: object
 *       required:
 *         - first_name
 *         - last_name
 *         - position
 *         - hire_date
 *         - salary
 *         - phone_number
 *         - email
 *         - address
 *       properties:
 *         first_name:
 *           type: string
 *           maxLength: 50
 *         last_name:
 *           type: string
 *           maxLength: 50
 *         position:
 *           type: string
 *           enum: [Manager, Cashier, Vendor, Barista]
 *         hire_date:
 *           type: string
 *           format: date
 *         salary:
 *           type: number
 *           format: decimal
 *           minimum: 0.01
 *         phone_number:
 *           type: string
 *           maxLength: 20
 *         email:
 *           type: string
 *           maxLength: 100
 *           format: email
 *         address:
 *           type: string
 *           maxLength: 255
 *       example:
 *         first_name: "Jane"
 *         last_name: "Smith"
 *         position: "Manager"
 *         hire_date: "2024-01-15"
 *         salary: 5000.00
 *         phone_number: "+1234567890"
 *         email: "<EMAIL>"
 *         address: "123 Main St, City, State"
 */

/**
 * @swagger
 * /api/employees:
 *   get:
 *     summary: Get all employees with pagination and filtering
 *     tags: [Employees]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of employees per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or email
 *       - in: query
 *         name: position
 *         schema:
 *           type: string
 *           enum: [Manager, Cashier, Vendor, Barista]
 *         description: Filter by position
 *     responses:
 *       200:
 *         description: Employees retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 *                 pagination:
 *                   type: object
 */
router.get('/', EmployeeController.getAllEmployees);

/**
 * @swagger
 * /api/employees/{id}:
 *   get:
 *     summary: Get employee by ID
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Employee'
 *       404:
 *         description: Employee not found
 */
router.get('/:id', EmployeeController.getEmployeeById);

/**
 * @swagger
 * /api/employees:
 *   post:
 *     summary: Create a new employee
 *     tags: [Employees]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmployeeInput'
 *     responses:
 *       201:
 *         description: Employee created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Employee'
 *       400:
 *         description: Validation error
 *       409:
 *         description: Email already exists
 */
router.post('/', EmployeeController.createEmployee);

/**
 * @swagger
 * /api/employees/{id}:
 *   put:
 *     summary: Update employee
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmployeeInput'
 *     responses:
 *       200:
 *         description: Employee updated successfully
 *       404:
 *         description: Employee not found
 *       409:
 *         description: Email already exists
 */
router.put('/:id', EmployeeController.updateEmployee);

/**
 * @swagger
 * /api/employees/{id}:
 *   delete:
 *     summary: Delete employee
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Employee ID
 *     responses:
 *       200:
 *         description: Employee deleted successfully
 *       404:
 *         description: Employee not found
 *       409:
 *         description: Cannot delete employee with existing records
 */
router.delete('/:id', EmployeeController.deleteEmployee);

/**
 * @swagger
 * /api/employees/position/{position}:
 *   get:
 *     summary: Get employees by position
 *     tags: [Employees]
 *     parameters:
 *       - in: path
 *         name: position
 *         required: true
 *         schema:
 *           type: string
 *           enum: [Manager, Cashier, Vendor, Barista]
 *         description: Employee position
 *     responses:
 *       200:
 *         description: Employees retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Employee'
 *       400:
 *         description: Invalid position
 */
router.get('/position/:position', EmployeeController.getEmployeesByPosition);

export default router;
