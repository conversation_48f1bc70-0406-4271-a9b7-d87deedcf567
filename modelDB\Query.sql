-- 🔄 TRIGGER: Auto-insert loyalty points based on order quantity after payment
DELIMITER //

CREATE TRIGGER after_payment_insert
AFTER INSERT ON Payments
FOR EACH ROW
BEGIN
    DECLARE order_qty INT;

    -- Get quantity from order
    SELECT quantity INTO order_qty
    FROM orders
    WHERE order_id = NEW.order_id;

    -- Insert loyalty points
    INSERT INTO Loyalty_Points (order_id, points_earned)
    VALUES (NEW.order_id, order_qty);
END;
//

DELIMITER ;


-- view loyal point per customer

SELECT 
  c.customer_id,
  CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
  SUM(lp.points_earned) AS total_points
FROM loyalty_points lp
JOIN orders o ON lp.order_id = o.order_id
JOIN customers c ON o.customer_id = c.customer_id
GROUP BY c.customer_id;


-- sale of material_eating and material-object per month

SELECT 
    'eating' AS material_type,
    DATE_FORMAT(import_date, '%Y-%m') AS month,
    SUM(unit_price * quantity) AS total_cost
FROM material_eating
GROUP BY month

UNION

SELECT 
    'object' AS material_type,
    DATE_FORMAT(import_date, '%Y-%m') AS month,
    SUM(unit_price * quantity) AS total_cost
FROM material_object
GROUP BY month;


-- orders with no payment

SELECT 
    o.order_id,
    c.first_name,
    c.last_name,
    p.product_name,
    o.quantity,
    o.unit_price
FROM orders o
JOIN customers c ON o.customer_id = c.customer_id
JOIN products p ON o.product_id = p.product_id
LEFT JOIN payments pay ON o.order_id = pay.order_id
WHERE pay.order_id IS NULL;


-- list of product with zero quantity material

SELECT 
    pr.product_name,
    me.name_material AS material_eating,
    mo.name_material AS material_object
FROM products pr
LEFT JOIN material_eating me ON pr.material_eating_id = me.material_eating_id
LEFT JOIN material_object mo ON pr.material_object_id = mo.material_object_id
WHERE (me.quantity = 0 OR mo.quantity = 0);


-- material cost per product (raw cost view)
CREATE VIEW raw_material_cost_per_product AS
SELECT 
    p.product_name,
    IFNULL(me.unit_price, 0) + IFNULL(mo.unit_price, 0) AS raw_material_cost
FROM products p
LEFT JOIN material_eating me ON p.material_eating_id = me.material_eating_id
LEFT JOIN material_object mo ON p.material_object_id = mo.material_object_id;


-- sales in last 7 days

SELECT 
    DATE(order_date) AS date,
    SUM(quantity * unit_price) AS total_sales
FROM orders
WHERE order_date >= NOW() - INTERVAL 7 DAY
GROUP BY DATE(order_date)
ORDER BY date DESC;


-- product sold the most (by quantity)

SELECT 
    p.product_name,
    SUM(o.quantity) AS total_quantity_sold
FROM orders o
JOIN products p ON o.product_id = p.product_id
GROUP BY o.product_id
ORDER BY total_quantity_sold DESC
LIMIT 1;


-- check stock for material_object and material_eating

SELECT 'object' AS type, name_material, quantity
FROM material_object

UNION

SELECT 'eating' AS type, name_material, quantity
FROM material_eating;


-- spending on supply (grouped by supplier)

SELECT 
    s.supplies_name,
    s.category,
    SUM(IFNULL(me.unit_price * me.quantity, 0) + IFNULL(mo.unit_price * mo.quantity, 0)) AS total_spent
FROM suppliers s
LEFT JOIN material_eating me ON s.supplier_id = me.supplier_id
LEFT JOIN material_object mo ON s.supplier_id = mo.supplier_id
GROUP BY s.supplier_id;


-- view customer order history (product per customer)

SELECT 
  c.customer_id,
  CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
  o.order_id,
  p.product_name,
  o.quantity,
  o.unit_price,
  o.order_date
FROM orders o
JOIN customers c ON o.customer_id = c.customer_id
JOIN products p ON o.product_id = p.product_id
ORDER BY c.customer_id, o.order_date;

-- view sale in a month

SELECT 
  DATE_FORMAT(order_date, '%Y-%m') AS month,
  SUM(quantity * unit_price) AS total_sales
FROM orders
GROUP BY month
ORDER BY month DESC;

-- view total revenue by product

SELECT 
  p.product_name,
  SUM(o.quantity * o.unit_price) AS total_revenue
FROM orders o
JOIN products p ON o.product_id = p.product_id
GROUP BY p.product_id
ORDER BY total_revenue DESC;

-- view top 5 customers by spending

SELECT 
  c.customer_id,
  CONCAT(c.first_name, ' ', c.last_name) AS customer_name,
  SUM(o.quantity * o.unit_price) AS total_spent
FROM orders o
JOIN customers c ON o.customer_id = c.customer_id
GROUP BY c.customer_id
ORDER BY total_spent DESC
LIMIT 5;

-- view unused products (never ordered)

SELECT 
  p.product_id,
  p.product_name
FROM products p
LEFT JOIN orders o ON p.product_id = o.product_id
WHERE o.order_id IS NULL;

-- view most frequently ordered product (by order count)

SELECT 
  p.product_name,
  COUNT(o.order_id) AS times_ordered
FROM orders o
JOIN products p ON o.product_id = p.product_id
GROUP BY o.product_id
ORDER BY times_ordered DESC
LIMIT 1;

-- view monthly order count and revenue

SELECT 
  DATE_FORMAT(order_date, '%Y-%m') AS month,
  COUNT(order_id) AS total_orders,
  SUM(quantity * unit_price) AS total_revenue
FROM orders
GROUP BY month
ORDER BY month DESC;
