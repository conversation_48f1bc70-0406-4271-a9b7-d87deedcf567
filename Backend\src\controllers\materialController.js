import DatabaseService from '../services/databaseService.js';

/**
 * Material Controller
 * Handles both material_eating and material_object operations
 */

class MaterialController {
    /**
     * Get all eating materials with pagination and filtering
     */
    static async getAllEatingMaterials(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search || '';
            const category = req.query.category || '';
            const supplier_id = req.query.supplier_id || '';

            let baseQuery = `
                SELECT 
                    me.material_eating_id,
                    me.name_material,
                    me.quantity,
                    me.unit_price,
                    me.import_date,
                    me.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_eating me
                LEFT JOIN suppliers s ON me.supplier_id = s.supplier_id
                LEFT JOIN employees e ON me.employee_id = e.employee_id
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (search) {
                whereConditions.push('me.name_material LIKE ?');
                params.push(`%${search}%`);
            }
            
            if (category) {
                whereConditions.push('me.category = ?');
                params.push(category);
            }
            
            if (supplier_id) {
                whereConditions.push('me.supplier_id = ?');
                params.push(supplier_id);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY me.import_date DESC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Eating materials retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting eating materials:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve eating materials',
                error: error.message
            });
        }
    }

    /**
     * Get all object materials with pagination and filtering
     */
    static async getAllObjectMaterials(req, res) {
        try {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 10;
            const search = req.query.search || '';
            const category = req.query.category || '';
            const supplier_id = req.query.supplier_id || '';

            let baseQuery = `
                SELECT 
                    mo.material_object_id,
                    mo.name_material,
                    mo.quantity,
                    mo.unit_price,
                    mo.import_date,
                    mo.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_object mo
                LEFT JOIN suppliers s ON mo.supplier_id = s.supplier_id
                LEFT JOIN employees e ON mo.employee_id = e.employee_id
            `;
            
            let params = [];
            let whereConditions = [];
            
            if (search) {
                whereConditions.push('mo.name_material LIKE ?');
                params.push(`%${search}%`);
            }
            
            if (category) {
                whereConditions.push('mo.category = ?');
                params.push(category);
            }
            
            if (supplier_id) {
                whereConditions.push('mo.supplier_id = ?');
                params.push(supplier_id);
            }
            
            if (whereConditions.length > 0) {
                baseQuery += ` WHERE ${whereConditions.join(' AND ')}`;
            }
            
            baseQuery += ` ORDER BY mo.import_date DESC`;

            const result = await DatabaseService.getPaginatedResults(baseQuery, params, page, limit);
            
            res.json({
                success: true,
                message: 'Object materials retrieved successfully',
                ...result
            });
        } catch (error) {
            console.error('Error getting object materials:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve object materials',
                error: error.message
            });
        }
    }

    /**
     * Create new eating material
     */
    static async createEatingMaterial(req, res) {
        try {
            const { 
                supplier_id, 
                name_material, 
                quantity, 
                unit_price, 
                category, 
                employee_id 
            } = req.body;

            // Validation
            if (!supplier_id || !name_material || !quantity || !unit_price || !category || !employee_id) {
                return res.status(400).json({
                    success: false,
                    message: 'All fields are required'
                });
            }

            // Validate quantity and unit_price are positive
            if (quantity <= 0 || unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Quantity and unit_price must be greater than 0'
                });
            }

            // Validate category enum
            const validCategories = [
                'coffee bean', 'matcha', 'sugar', 'condensed milk', 'fresh milk', 'espresso',
                'chocolate', 'green tea', 'black tea', 'caramel syrup', 'vanilla syrup',
                'hazelnut syrup', 'ice', 'whipped cream', 'honey', 'almond milk',
                'soy milk', 'coconut milk', 'mint', 'lemon juice'
            ];
            
            if (!validCategories.includes(category)) {
                return res.status(400).json({
                    success: false,
                    message: `Category must be one of: ${validCategories.join(', ')}`
                });
            }

            // Check if supplier and employee exist
            const supplierExists = await DatabaseService.recordExists('suppliers', 'supplier_id', supplier_id);
            const employeeExists = await DatabaseService.recordExists('employees', 'employee_id', employee_id);

            if (!supplierExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found'
                });
            }

            if (!employeeExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found'
                });
            }

            const materialId = await DatabaseService.executeInsert(
                `INSERT INTO material_eating (supplier_id, name_material, quantity, unit_price, category, employee_id) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [supplier_id, name_material, quantity, unit_price, category, employee_id]
            );

            const newMaterial = await DatabaseService.executeQuerySingle(
                `SELECT 
                    me.material_eating_id,
                    me.name_material,
                    me.quantity,
                    me.unit_price,
                    me.import_date,
                    me.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_eating me
                LEFT JOIN suppliers s ON me.supplier_id = s.supplier_id
                LEFT JOIN employees e ON me.employee_id = e.employee_id
                WHERE me.material_eating_id = ?`,
                [materialId]
            );

            res.status(201).json({
                success: true,
                message: 'Eating material created successfully',
                data: newMaterial
            });
        } catch (error) {
            console.error('Error creating eating material:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create eating material',
                error: error.message
            });
        }
    }

    /**
     * Create new object material
     */
    static async createObjectMaterial(req, res) {
        try {
            const { 
                supplier_id, 
                name_material, 
                quantity, 
                unit_price, 
                category, 
                employee_id 
            } = req.body;

            // Validation
            if (!supplier_id || !name_material || !quantity || !unit_price || !category || !employee_id) {
                return res.status(400).json({
                    success: false,
                    message: 'All fields are required'
                });
            }

            // Validate quantity and unit_price are positive
            if (quantity <= 0 || unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Quantity and unit_price must be greater than 0'
                });
            }

            // Validate category enum
            const validCategories = [
                'tissue', 'plastic cup', 'paper cup', 'cup lid', 'straw', 'plastic bag',
                'napkin', 'glove', 'mask', 'tray', 'cleaning spray', 'dishwashing liquid',
                'cloth towel', 'toilet paper', 'garbage bag', 'ice bucket', 'measuring spoon',
                'milk frother', 'filter paper', 'receipt roll'
            ];
            
            if (!validCategories.includes(category)) {
                return res.status(400).json({
                    success: false,
                    message: `Category must be one of: ${validCategories.join(', ')}`
                });
            }

            // Check if supplier and employee exist
            const supplierExists = await DatabaseService.recordExists('suppliers', 'supplier_id', supplier_id);
            const employeeExists = await DatabaseService.recordExists('employees', 'employee_id', employee_id);

            if (!supplierExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Supplier not found'
                });
            }

            if (!employeeExists) {
                return res.status(404).json({
                    success: false,
                    message: 'Employee not found'
                });
            }

            const materialId = await DatabaseService.executeInsert(
                `INSERT INTO material_object (supplier_id, name_material, quantity, unit_price, category, employee_id) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [supplier_id, name_material, quantity, unit_price, category, employee_id]
            );

            const newMaterial = await DatabaseService.executeQuerySingle(
                `SELECT 
                    mo.material_object_id,
                    mo.name_material,
                    mo.quantity,
                    mo.unit_price,
                    mo.import_date,
                    mo.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_object mo
                LEFT JOIN suppliers s ON mo.supplier_id = s.supplier_id
                LEFT JOIN employees e ON mo.employee_id = e.employee_id
                WHERE mo.material_object_id = ?`,
                [materialId]
            );

            res.status(201).json({
                success: true,
                message: 'Object material created successfully',
                data: newMaterial
            });
        } catch (error) {
            console.error('Error creating object material:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create object material',
                error: error.message
            });
        }
    }

    /**
     * Update eating material quantity
     */
    static async updateEatingMaterial(req, res) {
        try {
            const { id } = req.params;
            const { quantity, unit_price } = req.body;

            if (quantity !== undefined && quantity < 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Quantity cannot be negative'
                });
            }

            if (unit_price !== undefined && unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Unit price must be greater than 0'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                `UPDATE material_eating 
                 SET quantity = COALESCE(?, quantity), unit_price = COALESCE(?, unit_price)
                 WHERE material_eating_id = ?`,
                [quantity, unit_price, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Eating material not found'
                });
            }

            const updatedMaterial = await DatabaseService.executeQuerySingle(
                `SELECT 
                    me.material_eating_id,
                    me.name_material,
                    me.quantity,
                    me.unit_price,
                    me.import_date,
                    me.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_eating me
                LEFT JOIN suppliers s ON me.supplier_id = s.supplier_id
                LEFT JOIN employees e ON me.employee_id = e.employee_id
                WHERE me.material_eating_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Eating material updated successfully',
                data: updatedMaterial
            });
        } catch (error) {
            console.error('Error updating eating material:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update eating material',
                error: error.message
            });
        }
    }

    /**
     * Update object material quantity
     */
    static async updateObjectMaterial(req, res) {
        try {
            const { id } = req.params;
            const { quantity, unit_price } = req.body;

            if (quantity !== undefined && quantity < 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Quantity cannot be negative'
                });
            }

            if (unit_price !== undefined && unit_price <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Unit price must be greater than 0'
                });
            }

            const affectedRows = await DatabaseService.executeUpdate(
                `UPDATE material_object 
                 SET quantity = COALESCE(?, quantity), unit_price = COALESCE(?, unit_price)
                 WHERE material_object_id = ?`,
                [quantity, unit_price, id]
            );

            if (affectedRows === 0) {
                return res.status(404).json({
                    success: false,
                    message: 'Object material not found'
                });
            }

            const updatedMaterial = await DatabaseService.executeQuerySingle(
                `SELECT 
                    mo.material_object_id,
                    mo.name_material,
                    mo.quantity,
                    mo.unit_price,
                    mo.import_date,
                    mo.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_object mo
                LEFT JOIN suppliers s ON mo.supplier_id = s.supplier_id
                LEFT JOIN employees e ON mo.employee_id = e.employee_id
                WHERE mo.material_object_id = ?`,
                [id]
            );

            res.json({
                success: true,
                message: 'Object material updated successfully',
                data: updatedMaterial
            });
        } catch (error) {
            console.error('Error updating object material:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update object material',
                error: error.message
            });
        }
    }

    /**
     * Get low stock materials (quantity <= 10)
     */
    static async getLowStockMaterials(req, res) {
        try {
            const lowStockEating = await DatabaseService.executeQuery(
                `SELECT 
                    'eating' as type,
                    me.material_eating_id as id,
                    me.name_material,
                    me.quantity,
                    me.category,
                    s.supplies_name as supplier_name
                FROM material_eating me
                LEFT JOIN suppliers s ON me.supplier_id = s.supplier_id
                WHERE me.quantity <= 10`
            );

            const lowStockObject = await DatabaseService.executeQuery(
                `SELECT 
                    'object' as type,
                    mo.material_object_id as id,
                    mo.name_material,
                    mo.quantity,
                    mo.category,
                    s.supplies_name as supplier_name
                FROM material_object mo
                LEFT JOIN suppliers s ON mo.supplier_id = s.supplier_id
                WHERE mo.quantity <= 10`
            );

            const lowStockMaterials = [...lowStockEating, ...lowStockObject];

            res.json({
                success: true,
                message: 'Low stock materials retrieved successfully',
                data: lowStockMaterials
            });
        } catch (error) {
            console.error('Error getting low stock materials:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve low stock materials',
                error: error.message
            });
        }
    }

    /**
     * Get material inventory summary
     */
    static async getInventorySummary(req, res) {
        try {
            const summary = await DatabaseService.executeQuery(
                `SELECT 'eating' AS type, name_material, quantity
                 FROM material_eating
                 UNION
                 SELECT 'object' AS type, name_material, quantity
                 FROM material_object
                 ORDER BY type, name_material`
            );

            res.json({
                success: true,
                message: 'Inventory summary retrieved successfully',
                data: summary
            });
        } catch (error) {
            console.error('Error getting inventory summary:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve inventory summary',
                error: error.message
            });
        }
    }

    /**
     * Get eating material by ID
     */
    static async getEatingMaterialById(req, res) {
        try {
            const { id } = req.params;

            const material = await DatabaseService.executeQuerySingle(
                `SELECT
                    me.material_eating_id,
                    me.name_material,
                    me.quantity,
                    me.unit_price,
                    me.import_date,
                    me.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_eating me
                LEFT JOIN suppliers s ON me.supplier_id = s.supplier_id
                LEFT JOIN employees e ON me.employee_id = e.employee_id
                WHERE me.material_eating_id = ?`,
                [id]
            );

            if (!material) {
                return res.status(404).json({
                    success: false,
                    message: 'Eating material not found'
                });
            }

            res.json({
                success: true,
                message: 'Eating material retrieved successfully',
                data: material
            });
        } catch (error) {
            console.error('Error getting eating material:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve eating material',
                error: error.message
            });
        }
    }

    /**
     * Get object material by ID
     */
    static async getObjectMaterialById(req, res) {
        try {
            const { id } = req.params;

            const material = await DatabaseService.executeQuerySingle(
                `SELECT
                    mo.material_object_id,
                    mo.name_material,
                    mo.quantity,
                    mo.unit_price,
                    mo.import_date,
                    mo.category,
                    s.supplies_name as supplier_name,
                    CONCAT(e.first_name, ' ', e.last_name) as employee_name
                FROM material_object mo
                LEFT JOIN suppliers s ON mo.supplier_id = s.supplier_id
                LEFT JOIN employees e ON mo.employee_id = e.employee_id
                WHERE mo.material_object_id = ?`,
                [id]
            );

            if (!material) {
                return res.status(404).json({
                    success: false,
                    message: 'Object material not found'
                });
            }

            res.json({
                success: true,
                message: 'Object material retrieved successfully',
                data: material
            });
        } catch (error) {
            console.error('Error getting object material:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to retrieve object material',
                error: error.message
            });
        }
    }
}

export default MaterialController;
